import com.autoai.car.buildsrc.Libs

import java.text.SimpleDateFormat
apply from: "${project.rootDir}/functions.gradle"

// Top-level build file where you can add configuration options common to all sub-projects/modules.


ext {
    isRdmEnv = System.getenv("rdmEnv") != null
    isDevopsEnv = System.getenv("DEVOPS_ENV") != null
    releaseMark = (isRdmEnv || isDevopsEnv)
    versionCodeDevOps = System.getenv("VERSION_CODE") != null ? Integer.parseInt(System.getenv("VERSION_CODE")) :
            1
    versionNameDevOps = System.getenv("VERSION_NAME") != null ? System.getenv("VERSION_NAME") :
            "*******"
    println("KTV_DEVOPS isRdmEnv: $isRdmEnv, isDevopsEnv:$isDevopsEnv")
    def date = new Date()
    def sdf = new SimpleDateFormat("yyyy_MM_dd HH:mm:ss")
    def dateString = sdf.format(date)
    sdkBuildVersion = versionNameDevOps + "_" + System.getenv("BK_CI_BUILD_NUM") + "_" + dateString
}

buildscript {
    apply from: "${project.rootDir}/versions.gradle"
    repositories {
        jcenter()
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://www.jitpack.io' }
        maven {
            allowInsecureProtocol true
            url 'http://syshome.autoai.com/artifactory/repository/sw-release/'
            credentials {
                username 'swdeveloper'
                password 'swdeveloper'
            }
        }
        maven {
            allowInsecureProtocol true
            url 'http://syshome.autoai.com/artifactory/repository/sw-snapshot/'
            credentials {
                username 'swdeveloper'
                password 'swdeveloper'
            }
        }
        mavenCentral()
        google()
    }

    dependencies {
        // gradle 插件
        classpath Libs.ProjectPluginManager.buildGradle
        // kotlin 插件
        classpath Libs.ProjectPluginManager.kotlinGradlePlugin
        // AspectJ 插件
        classpath Libs.ProjectPluginManager.AspectjxPlugin

    }
}

//apply from: "${project.rootDir}/config.gradle"

task clean(type: Delete) {
    delete rootProject.buildDir
}