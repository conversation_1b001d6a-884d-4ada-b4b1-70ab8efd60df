package com.autoai.common.utils

import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.asCoroutineDispatcher
import java.util.concurrent.Executors
import java.util.concurrent.ThreadFactory
import java.util.concurrent.atomic.AtomicInteger

/**
 * 守护线程调度器工具类
 * 用于创建守护线程的协程调度器，避免阻塞应用关闭
 *
 * @author: dongjs
 * @time: 2025/7/22
 * @desc: 解决非守护线程导致的ANR问题
 */
object DaemonThreadDispatcher {

    private const val TAG = "DaemonThreadDispatcher"

    // 标记调度器是否已关闭
    @Volatile
    private var isShutdown = false

    // 保存ExecutorService引用，支持重新创建
    @Volatile
    private var ioExecutor: java.util.concurrent.ExecutorService? = null

    @Volatile
    private var defaultExecutor: java.util.concurrent.ExecutorService? = null

    @Volatile
    private var singleExecutor: java.util.concurrent.ExecutorService? = null

    @Volatile
    private var databaseExecutor: java.util.concurrent.ExecutorService? = null

    /**
     * 创建IO线程池
     */
    private fun createIoExecutor(): java.util.concurrent.ExecutorService {
        return Executors.newCachedThreadPool(object : ThreadFactory {
            private val counter = AtomicInteger(0)
            override fun newThread(r: Runnable): Thread {
                val thread = Thread(r, "DaemonIO-${counter.incrementAndGet()}")
                thread.isDaemon = true
                return thread
            }
        })
    }

    /**
     * 创建默认线程池
     */
    private fun createDefaultExecutor(): java.util.concurrent.ExecutorService {
        val coreCount = Runtime.getRuntime().availableProcessors()
        return Executors.newFixedThreadPool(coreCount, object : ThreadFactory {
            private val counter = AtomicInteger(0)
            override fun newThread(r: Runnable): Thread {
                val thread = Thread(r, "DaemonDefault-${counter.incrementAndGet()}")
                thread.isDaemon = true
                return thread
            }
        })
    }

    /**
     * 创建单线程池
     */
    private fun createSingleExecutor(): java.util.concurrent.ExecutorService {
        return Executors.newSingleThreadExecutor(object : ThreadFactory {
            override fun newThread(r: Runnable): Thread {
                val thread = Thread(r, "DaemonSingle")
                thread.isDaemon = true
                return thread
            }
        })
    }

    /**
     * 创建数据库线程池
     */
    private fun createDatabaseExecutor(): java.util.concurrent.ExecutorService {
        return Executors.newFixedThreadPool(2, object : ThreadFactory {
            private val counter = AtomicInteger(0)
            override fun newThread(r: Runnable): Thread {
                val thread = Thread(r, "DaemonDB-${counter.incrementAndGet()}")
                thread.isDaemon = true
                return thread
            }
        })
    }

    /**
     * IO操作专用的守护线程调度器
     * 替代 Dispatchers.IO，使用守护线程避免阻塞应用关闭
     */
    val IO: CoroutineDispatcher
        get() = getOrCreateDispatcher("IO") {
            getOrCreateExecutor("IO") { createIoExecutor() }.asCoroutineDispatcher()
        }

    /**
     * CPU密集型操作专用的守护线程调度器
     * 替代 Dispatchers.Default，使用守护线程避免阻塞应用关闭
     */
    val Default: CoroutineDispatcher
        get() = getOrCreateDispatcher("Default") {
            getOrCreateExecutor("Default") { createDefaultExecutor() }.asCoroutineDispatcher()
        }

    /**
     * 单线程守护线程调度器
     * 用于需要串行执行的任务
     */
    val Single: CoroutineDispatcher
        get() = getOrCreateDispatcher("Single") {
            getOrCreateExecutor("Single") { createSingleExecutor() }.asCoroutineDispatcher()
        }

    /**
     * 数据库操作专用的守护线程调度器
     * 限制并发数，避免数据库连接过多
     */
    val Database: CoroutineDispatcher
        get() = getOrCreateDispatcher("Database") {
            getOrCreateExecutor("Database") { createDatabaseExecutor() }.asCoroutineDispatcher()
        }

    // 缓存已创建的调度器，避免重复创建
    private val dispatcherCache = mutableMapOf<String, CoroutineDispatcher>()

    /**
     * 获取或创建ExecutorService，支持重新创建
     */
    private fun getOrCreateExecutor(name: String, factory: () -> java.util.concurrent.ExecutorService): java.util.concurrent.ExecutorService {
        return synchronized(this) {
            val currentExecutor = when (name) {
                "IO" -> ioExecutor
                "Default" -> defaultExecutor
                "Single" -> singleExecutor
                "Database" -> databaseExecutor
                else -> null
            }

            // 检查ExecutorService是否可用（未关闭且未终止）
            if (currentExecutor != null && !currentExecutor.isShutdown && !currentExecutor.isTerminated) {
                return currentExecutor
            }

            // 需要创建新的ExecutorService
            Log.d(TAG, "Creating new $name executor")
            val newExecutor = factory()

            // 保存引用
            when (name) {
                "IO" -> ioExecutor = newExecutor
                "Default" -> defaultExecutor = newExecutor
                "Single" -> singleExecutor = newExecutor
                "Database" -> databaseExecutor = newExecutor
            }

            return newExecutor
        }
    }

    /**
     * 获取或创建调度器，如果已关闭则自动重新初始化
     */
    private fun getOrCreateDispatcher(name: String, factory: () -> CoroutineDispatcher): CoroutineDispatcher {
        return synchronized(this) {
            if (isShutdown) {
                Log.w(TAG, "DaemonThreadDispatcher was shutdown, auto-reinitializing for $name dispatcher")
                // 自动重新初始化
                isShutdown = false
                dispatcherCache.clear()
            }

            dispatcherCache.getOrPut(name) {
                try {
                    factory()
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to create $name dispatcher", e)
                    // 如果创建失败，返回标准调度器作为备选
                    when (name) {
                        "IO" -> Dispatchers.IO
                        "Default" -> Dispatchers.Default
                        "Single" -> Dispatchers.IO.limitedParallelism(1)
                        "Database" -> Dispatchers.IO.limitedParallelism(4)
                        else -> Dispatchers.IO
                    }
                }
            }
        }
    }

    /**
     * 检查调度器是否可用
     * 注意：由于现在支持自动重新初始化，这个方法主要用于日志和监控
     */
    fun isAvailable(): Boolean {
        return !isShutdown
    }
    
    /**
     * 清理所有调度器资源
     * 在应用退出时调用
     */
    fun shutdown() {
        synchronized(this) {
            try {
                Log.d(TAG, "Shutting down daemon thread dispatchers")
                isShutdown = true

                // 清理调度器缓存
                dispatcherCache.clear()

                // 正确关闭ExecutorService，给正在执行的任务一些时间完成
                ioExecutor?.let {
                    shutdownExecutorGracefully(it, "IO")
                    ioExecutor = null
                }
                defaultExecutor?.let {
                    shutdownExecutorGracefully(it, "Default")
                    defaultExecutor = null
                }
                singleExecutor?.let {
                    shutdownExecutorGracefully(it, "Single")
                    singleExecutor = null
                }
                databaseExecutor?.let {
                    shutdownExecutorGracefully(it, "Database")
                    databaseExecutor = null
                }



                Log.d(TAG, "Daemon thread dispatchers shutdown completed")
            } catch (e: Exception) {
                Log.e(TAG, "Error during dispatcher shutdown", e)
            }
        }
    }

    /**
     * 手动重新初始化调度器
     * 主要用于测试或特殊恢复场景
     */
    fun reinitialize() {
        synchronized(this) {
            Log.d(TAG, "Manually reinitializing daemon thread dispatchers")
            isShutdown = false
            dispatcherCache.clear()

            // 清空ExecutorService引用，下次访问时会重新创建
            ioExecutor = null
            defaultExecutor = null
            singleExecutor = null
            databaseExecutor = null
        }
    }

    /**
     * 优雅地关闭ExecutorService
     * 先尝试正常关闭，如果超时则强制关闭
     */
    private fun shutdownExecutorGracefully(executor: java.util.concurrent.ExecutorService, name: String) {
        try {
            Log.d(TAG, "Shutting down $name executor")
            executor.shutdown()

            // 等待最多2秒让任务完成
            if (!executor.awaitTermination(2, java.util.concurrent.TimeUnit.SECONDS)) {
                Log.w(TAG, "$name executor did not terminate gracefully, forcing shutdown")
                executor.shutdownNow()

                // 再等待1秒确认强制关闭
                if (!executor.awaitTermination(1, java.util.concurrent.TimeUnit.SECONDS)) {
                    Log.e(TAG, "$name executor did not terminate after forced shutdown")
                }
            }
            Log.d(TAG, "$name executor shutdown completed")
        } catch (e: InterruptedException) {
            Log.w(TAG, "$name executor shutdown interrupted", e)
            executor.shutdownNow()
            Thread.currentThread().interrupt()
        } catch (e: Exception) {
            Log.e(TAG, "Error shutting down $name executor", e)
        }
    }
}
