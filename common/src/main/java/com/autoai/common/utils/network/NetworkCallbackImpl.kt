package com.autoai.common.utils.network
import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkInfo
import android.net.NetworkRequest
import com.autoai.common.BaseApplication
import com.sgmw.common.utils.Log
import com.autoai.common.utils.DaemonThreadDispatcher
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.atomic.AtomicBoolean


/**
 * Network callback impl
 *
 * @constructor Create empty Network callback impl
 */
class NetworkCallbackImpl : ConnectivityManager.NetworkCallback() {

    private val TAG = NetworkCallbackImpl::class.simpleName

    /**
     * 当前网络类型
     */
    var currentNetworkType: NetworkTypeEnum = NetworkTypeEnum.OTHER

    /**
     * 当前网络是否已连接
     */
    var isConnected = AtomicBoolean(false)

    private var mConnectivityManager: ConnectivityManager? = null

    // 网络回调的协程作用域，使用守护线程避免阻塞应用关闭
    private var networkScope: CoroutineScope? = null

    init {
        // 初始化协程作用域，使用自定义异常处理器
        val exceptionHandler = CoroutineExceptionHandler { _, exception ->
            Log.e(TAG, "NetworkCallback coroutine exception", exception)
        }
        networkScope = CoroutineScope(DaemonThreadDispatcher.Default + SupervisorJob() + exceptionHandler)

        // 初始化网络连接状态
        mConnectivityManager = BaseApplication.context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        try {
            val build = NetworkRequest.Builder()
                .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                .addTransportType(NetworkCapabilities.TRANSPORT_WIFI)
                .addTransportType(NetworkCapabilities.TRANSPORT_CELLULAR)
                .build()
            mConnectivityManager?.registerNetworkCallback(build, this)
        } catch (e: Exception) {
            Log.e(TAG, e)
        }
        isConnected.set(isNetworkAvailable())
        Log.i(TAG, "init isConnected=${isConnected.get()}, currentNetworkType=$currentNetworkType")
    }

    /**
     * 注册的监听
     */
    private val networkCallBackList = CopyOnWriteArrayList<NetworkStateChangeListener?>()

    fun addNetworkListener(listener: NetworkStateChangeListener?) {
        listener?.let {
            if (!networkCallBackList.contains(listener)) {
                networkCallBackList.add(listener)
            }
        }
    }

    fun removeListener(listener: NetworkStateChangeListener?) {
        networkCallBackList.remove(listener)
    }

    /**
     * 清理所有监听器和资源，防止内存泄露
     */
    fun cleanup() {
        try {
            Log.d(TAG, "Cleaning up NetworkCallbackImpl resources")

            // 清理所有监听器
            networkCallBackList.clear()

            // 取消注册网络回调
            mConnectivityManager?.unregisterNetworkCallback(this)
            mConnectivityManager = null

            // 取消协程作用域
            networkScope?.cancel()
            networkScope = null

            Log.d(TAG, "NetworkCallbackImpl cleanup completed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during NetworkCallbackImpl cleanup", e)
        }
    }

    /**
     * 网络连接成功（当收到该回调时，不能认为网络可用，需要在 onCapabilitiesChanged 进一步判断）
     */
    override fun onAvailable(network: Network) {
        super.onAvailable(network)
        val oldHasNet = isConnected.get()
        val newHasNet = isNetworkAvailable()
        Log.i(TAG, "onAvailable: oldHasNet=$oldHasNet, newHasNet=$newHasNet, Thread=${Thread.currentThread().name}")
    }

    /**
     * 网络已断开连接
     */
    override fun onLost(network: Network) {
        super.onLost(network)
        val oldHasNet = isConnected.get()
        val newHasNet = isNetworkAvailable()
        Log.i(TAG, "onLost: oldHasNet=$oldHasNet, newHasNet=$newHasNet, Thread=${Thread.currentThread().name}")

        // 通知网络连接状态改变
        if (isConnected.get()) {
            isConnected.set(false)
            notifyNetworkChange()
        }
    }

    private fun notifyNetworkChange() {
        Log.i(TAG, "notifyNetworkChange: isConnected=${isConnected.get()}")
        networkCallBackList.forEach { callBack ->
            callBack?.networkConnectChange(isConnected.get())
            Log.i(
                TAG,
                "notifyNetworkChange: isConnected=${isConnected.get()}, Thread=${Thread.currentThread().name}"
            )
            // 网路状态变更，切回主线程回调 - 使用管理的协程作用域
            networkScope?.launch(Dispatchers.Main) {
                try {
                    Log.i(TAG, "notifyNetworkChange: inner Thread=${Thread.currentThread().name}")
                    networkCallBackList.forEach { callBack ->
                        callBack?.networkConnectChange(isConnected.get())
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error in network change notification", e)
                }
            }
        }
    }
        override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
            super.onCapabilitiesChanged(network, networkCapabilities)
            val newConnected = isNetworkAvailable()
            val oldConnected = isConnected.get()
            Log.d(TAG, "onCapabilitiesChanged: oldConnected=$oldConnected, newConnected=$newConnected, networkCapabilities=$networkCapabilities")

            // 通知网络连接状态改变
            if (oldConnected != newConnected) {
                isConnected.set(newConnected)
                notifyNetworkChange()
            }

            if (networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)) {
                currentNetworkType = networkTypeConvert(networkCapabilities)

                networkCallBackList?.forEach { callBack ->
                    callBack?.networkTypeChange(currentNetworkType)
                }
            }
        }

        /**
         * 网络类型转换
         */
        private fun networkTypeConvert(networkCapabilities: NetworkCapabilities): NetworkTypeEnum {
            return when {
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> {
                    NetworkTypeEnum.TRANSPORT_CELLULAR
                }
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> {
                    NetworkTypeEnum.TRANSPORT_WIFI
                }
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_BLUETOOTH) -> {
                    NetworkTypeEnum.TRANSPORT_BLUETOOTH
                }
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> {
                    NetworkTypeEnum.TRANSPORT_ETHERNET
                }
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_VPN) -> {
                    NetworkTypeEnum.TRANSPORT_VPN
                }
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI_AWARE) -> {
                    NetworkTypeEnum.TRANSPORT_WIFI_AWARE
                }
                networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_LOWPAN) -> {
                    NetworkTypeEnum.TRANSPORT_LOWPAN
                }
                else -> NetworkTypeEnum.OTHER
            }
        }

    /**
     * 网络状态是否可用
     */
    fun isNetworkAvailable(): Boolean {
         // 网络状态相关先替换成此api
        val networkInfo: NetworkInfo? = mConnectivityManager?.activeNetworkInfo
        val isConnected = networkInfo != null && networkInfo.isConnected
        Log.d(TAG,"isNetworkAvailable == $isConnected")
        return isConnected
//        if (mConnectivityManager != null) {
//            val network: Network? = mConnectivityManager?.getActiveNetwork()
//            if (network != null) {
//                Log.i(TAG, "network != null")
//                val networkCapabilities: NetworkCapabilities? =
//                    mConnectivityManager?.getNetworkCapabilities(network)
//                if (networkCapabilities != null) {
//                    Log.i(TAG, "networkCapabilities != null")
//                    val validated =
//                        networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
//                    Log.i(TAG, "validated = $validated")
//                    return validated
//
//                }
//            }
//        }
//        return false
    }
}