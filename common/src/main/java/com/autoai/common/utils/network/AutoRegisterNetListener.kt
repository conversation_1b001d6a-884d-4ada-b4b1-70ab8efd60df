package com.autoai.common.utils.network
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import com.sgmw.common.utils.Log

/**
 * Auto register net listener
 *
 * @property listener
 * @constructor Create empty Auto register net listener
 */
class AutoRegisterNetListener constructor(private val listener: NetworkStateChangeListener) :
    LifecycleObserver {

    private val TAG = AutoRegisterNetListener::class.simpleName

    @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
    fun register() {
        Log.i(TAG, "register NetworkStateClient $listener")
        this.listener?.run { NetworkStateClient.addListener(this) }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_PAUSE)
    fun unregister() {
        NetworkStateClient.removeListener(listener)
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun clean() {
        NetworkStateClient.removeListener(listener)
    }
}