package com.autoai.common.ktx

import android.app.Activity
import android.content.Context
import android.provider.Settings
import android.util.DisplayMetrics
import android.view.WindowManager
import com.autoai.common.BaseApplication
import java.math.RoundingMode
import java.text.DecimalFormat

/**
 * @author: 董俊帅
 * @time: 2025/3/10
 * @desc:
 */

fun Activity.getScreenBrightness(): Float {
    val brightness = window?.attributes?.screenBrightness ?: -1f
    // -1.0f 表示跟随系统亮度设置
    return if (brightness != -1f) {
        brightness
    } else {
        getSystemBrightness()
    }
}


fun Context.getSystemBrightness(): Float {
    return try {
        val brightness = Settings.System.getInt(
            contentResolver,
            Settings.System.SCREEN_BRIGHTNESS
        )
        // 将 0~255 转换为 0.0f~1.0f 的范围
        brightness / 255f
    } catch (e: Settings.SettingNotFoundException) {
        e.printStackTrace()
        -1f
    }
}

/**
 * 四舍五入并保留1位小数
 */
fun Float.toFloatRoundOne(): Float {
    //指定保留一位小数
    val df = DecimalFormat("#.0")
    df.roundingMode = RoundingMode.HALF_UP
    df.minimumFractionDigits = 1
    return df.format(this.toDouble()).toFloat()
}

fun getDisplayMetrics(): DisplayMetrics {
    val displayMetrics = DisplayMetrics()
    (BaseApplication.context.getSystemService(
        Context.WINDOW_SERVICE
    ) as WindowManager).defaultDisplay.getRealMetrics(
        displayMetrics
    )
    return displayMetrics
}
