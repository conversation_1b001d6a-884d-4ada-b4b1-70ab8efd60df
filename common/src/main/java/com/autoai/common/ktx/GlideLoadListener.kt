package com.sgmw.common.ktx


import android.graphics.drawable.Drawable
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target

/**
 * Glide图片加载成功/失败的回调
 *<AUTHOR>
 *@date  2024/5/20
 */

class GlideLoadListener(private val onSuccess:(Int, Int) -> Unit, private val onFailure: () -> Unit) :
    RequestListener<Drawable> {

    override fun onLoadFailed(
        e: GlideException?,
        model: Any?,
        target: Target<Drawable>,
        isFirstResource: Boolean
    ): Boolean {
        onFailure()
        return false
    }

    override fun onResourceReady(
        resource: Drawable,
        model: Any,
        target: Target<Drawable>?,
        dataSource: DataSource,
        isFirstResource: Boolean
    ): Boolean {
        onSuccess(resource.intrinsicWidth, resource.intrinsicHeight)
        return false
    }
}
