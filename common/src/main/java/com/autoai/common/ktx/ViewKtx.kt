package com.autoai.common.ktx

import android.view.View

/**
 * @author: 董俊帅
 * @time: 2025/3/1
 * @desc: View的一些扩展方法
 */

// OnClickListener 接口
fun View.setOnSingleClickListener(
    interval: Long = 500,  // 新增参数：可自定义间隔（默认 500ms）
    listener: View.OnClickListener
) {
    setOnClickListener(SingleClickListener(interval, listener))
}

// Lambda 表达式 方式
fun View.setOnSingleClickListener(
    interval: Long = 500,  // 新增参数：可自定义间隔（默认 500ms）
    listener: (View) -> Unit
) {
    setOnClickListener(SingleClickListener(interval, View.OnClickListener { listener(it) }))
}

// 内部实现类（带间隔参数）
private class SingleClickListener(
    private val interval: Long,         // 动态接收间隔时间
    private val originListener: View.OnClickListener
) : View.OnClickListener {

    private var lastClickTime = 0L

    override fun onClick(v: View) {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastClickTime >= interval) {  // 使用动态间隔
            lastClickTime = currentTime
            originListener.onClick(v)
        }
    }
}


