#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1609008 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=35164, tid=41240
#
# JRE version: OpenJDK Runtime Environment (21.0.6) (build 21.0.6+-13391695-b895.109)
# Java VM: OpenJDK 64-Bit Server VM (21.0.6+-13391695-b895.109, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=CN -Duser.language=zh -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.13-bin\5xuhj0ry160q40clulazy9h7d\gradle-8.13\lib\agents\gradle-instrumentation-agent-8.13.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.13

Host: 13th Gen Intel(R) Core(TM) i7-13700H, 20 cores, 31G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4768)
Time: Tue Aug 26 19:48:05 2025  Windows 11 , 64 bit Build 26100 (10.0.26100.4768) elapsed time: 154.690940 seconds (0d 0h 2m 34s)

---------------  T H R E A D  ---------------

Current thread (0x0000021658c3da40):  JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=41240, stack(0x000000752da00000,0x000000752db00000) (1024K)]


Current CompileTask:
C2:154691 20759       4       org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$InvocationHandlerImpl::invoke (176 bytes)

Stack: [0x000000752da00000,0x000000752db00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d0639]
V  [jvm.dll+0x85eb03]
V  [jvm.dll+0x86105e]
V  [jvm.dll+0x861743]
V  [jvm.dll+0x27e6e6]
V  [jvm.dll+0xbff6d]
V  [jvm.dll+0xc04a3]
V  [jvm.dll+0x3b61ab]
V  [jvm.dll+0x1de684]
V  [jvm.dll+0x247dd4]
V  [jvm.dll+0x247251]
V  [jvm.dll+0x1c5f04]
V  [jvm.dll+0x2569ac]
V  [jvm.dll+0x254ef6]
V  [jvm.dll+0x3f0e96]
V  [jvm.dll+0x806ed8]
V  [jvm.dll+0x6cef0d]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000216595de870, length=289, elements={
0x000002162bde2140, 0x0000021646461160, 0x0000021646463bc0, 0x0000021646443d20,
0x00000216464408a0, 0x00000216464415c0, 0x0000021646443000, 0x000002164646efc0,
0x0000021646483700, 0x0000021646442970, 0x0000021646441c50, 0x00000216464422e0,
0x0000021646440f30, 0x0000021646443690, 0x000002164c97c6c0, 0x000002164c97da70,
0x000002164c97f4b0, 0x000002164c97ee20, 0x000002164c97cd50, 0x000002164c980860,
0x000002164c97d3e0, 0x000002164c97e100, 0x000002164c97fb40, 0x000002164c9801d0,
0x000002164c980ef0, 0x000002164c981580, 0x000002164c981c10, 0x000002164c97e790,
0x000002164c9822a0, 0x000002164c982930, 0x000002164c982fc0, 0x000002164c983650,
0x000002164c983ce0, 0x000002164eb2fe10, 0x000002164eb33290, 0x000002164eb30b30,
0x000002164eb304a0, 0x000002164eb311c0, 0x000002164eb32570, 0x000002164eb31850,
0x000002164eb31ee0, 0x000002164eb32c00, 0x000002164dfb5ab0, 0x000002164dfb8210,
0x000002164dfb74f0, 0x000002164dfb6140, 0x000002164dfb67d0, 0x000002164dfb6e60,
0x000002164dfb7b80, 0x000002164dfb5420, 0x000002164dfba970, 0x000002164dfbbd20,
0x000002164dfbc3b0, 0x000002164dfb8f30, 0x000002164dfb95c0, 0x000002164dfbb000,
0x000002164dfbb690, 0x000002165148ae40, 0x000002165148b4d0, 0x0000021651488050,
0x000002165148a120, 0x00000216514886e0, 0x0000021651489400, 0x0000021651488d70,
0x000002165148bb60, 0x000002165148c1f0, 0x000002165148c880, 0x000002165148cf10,
0x000002165148a7b0, 0x0000021651489a90, 0x000002165148dc30, 0x000002165148e2c0,
0x000002165148d5a0, 0x000002165148efe0, 0x000002165148e950, 0x000002165148f670,
0x00000216515f2760, 0x00000216515f3480, 0x00000216515f2df0, 0x00000216515f3b10,
0x00000216515f13b0, 0x00000216515f41a0, 0x00000216515f0d20, 0x00000216515f4830,
0x00000216515f4ec0, 0x00000216515f1a40, 0x00000216515f5be0, 0x00000216515f7620,
0x00000216515f5550, 0x00000216515f6270, 0x00000216515f6900, 0x000002164ba284a0,
0x000002164ba27e10, 0x000002164ba29850, 0x000002164ba291c0, 0x000002164ba263d0,
0x000002164ba29ee0, 0x000002164ba26a60, 0x000002164ba2a570, 0x000002164ba270f0,
0x000002164ba28b30, 0x000002164ba2ac00, 0x000002164ba27780, 0x000002164ba2c640,
0x000002164ba2b290, 0x000002164ba2d9f0, 0x000002164ba2ccd0, 0x000002164ba2b920,
0x000002164ba2d360, 0x000002164ba2bfb0, 0x000002164f8efee0, 0x000002164f8ef1c0,
0x000002164f8f0570, 0x000002164f8f1290, 0x000002164f8f1920, 0x000002164f8ee4a0,
0x000002164f8ef850, 0x000002164f8eeb30, 0x000002164f8f2cd0, 0x000002164f8f3360,
0x000002164f8f39f0, 0x000002164f8f4080, 0x000002164f8f0c00, 0x000002164f8f4710,
0x000002164f8f4da0, 0x000002164f8f1fb0, 0x000002164f8f5430, 0x000002164f8f5ac0,
0x000002164f8f2640, 0x000002164fea5740, 0x000002164fea7180, 0x000002164fea6af0,
0x000002164fea5dd0, 0x000002164fea4a20, 0x000002164fea7810, 0x000002164fea50b0,
0x000002164fea6460, 0x000002164fea4390, 0x000002164feaac90, 0x000002164feab320,
0x000002164fea98e0, 0x000002164fea7ea0, 0x000002164fea9f70, 0x000002164fea9250,
0x000002164feab9b0, 0x000002164fea8530, 0x0000021651aae0d0, 0x0000021651ab01a0,
0x0000021651aacd20, 0x0000021651ab0830, 0x0000021651aad3b0, 0x0000021651ab0ec0,
0x0000021651ab1be0, 0x0000021651ab1550, 0x0000021651ab2f90, 0x0000021651ab2270,
0x0000021651ab2900, 0x0000021651ab3620, 0x0000021646d30c90, 0x0000021646d2f8e0,
0x0000021646d31320, 0x0000021646d333f0, 0x0000021646d326d0, 0x0000021646d2ff70,
0x0000021646d30600, 0x0000021646d33a80, 0x0000021646d32d60, 0x0000021646d32040,
0x0000021646d34110, 0x0000021646d347a0, 0x0000021646d34e30, 0x0000021646d319b0,
0x0000021646d361e0, 0x0000021646d36870, 0x0000021646d382b0, 0x0000021646d36f00,
0x0000021646d35b50, 0x0000021646d37590, 0x0000021646d354c0, 0x0000021646d3b0a0,
0x0000021646d39cf0, 0x0000021646d37c20, 0x0000021646d38fd0, 0x0000021646d39660,
0x0000021646d3bdc0, 0x0000021646d3b730, 0x0000021646d38940, 0x0000021646d3d170,
0x0000021646d3c450, 0x0000021646d3aa10, 0x0000021646d3d800, 0x0000021646d3cae0,
0x0000021646d3de90, 0x0000021646d3e520, 0x0000021650213ef0, 0x0000021650214580,
0x0000021650211e20, 0x00000216502152a0, 0x0000021650215930, 0x00000216502124b0,
0x0000021650213860, 0x0000021650212b40, 0x0000021650214c10, 0x0000021650215fc0,
0x00000216502131d0, 0x0000021650217a00, 0x0000021650216ce0, 0x0000021650217370,
0x0000021646d3ebb0, 0x000002164d984d20, 0x000002164d989be0, 0x000002164d986760,
0x000002164d98b620, 0x000002164d98d6f0, 0x000002164d98af90, 0x000002164d98a270,
0x000002164d98d060, 0x000002164d98c9d0, 0x000002164d98bcb0, 0x000002164d98dd80,
0x000002164d98c340, 0x000002164d98a900, 0x000002164d98f7c0, 0x000002164d9904e0,
0x000002164d98eaa0, 0x000002164d990b70, 0x000002164d991200, 0x000002164d98e410,
0x000002164d98f130, 0x000002164d991890, 0x000002164d991f20, 0x000002164d9925b0,
0x000002164d992c40, 0x000002164d98fe50, 0x0000021650218090, 0x0000021651293d10,
0x00000216512943a0, 0x00000216512915b0, 0x0000021651294a30, 0x0000021651292960,
0x0000021651293680, 0x00000216512950c0, 0x0000021651292ff0, 0x0000021651291c40,
0x0000021651295750, 0x00000216512922d0, 0x0000021651295de0, 0x0000021651299260,
0x0000021651296b00, 0x0000021651297190, 0x0000021651296470, 0x00000216512998f0,
0x000002165129aca0, 0x0000021651299f80, 0x0000021651297820, 0x000002165129b330,
0x0000021651297eb0, 0x000002165129b9c0, 0x000002165129a610, 0x0000021651298bd0,
0x0000021651298540, 0x000002165129d400, 0x000002165129da90, 0x000002165129e120,
0x000002165129ee40, 0x000002165129cd70, 0x000002165129e7b0, 0x00000216579e5740,
0x00000216579e9250, 0x00000216579ea600, 0x0000021652923640, 0x0000021652922920,
0x00000216579e6460, 0x00000216579e6af0, 0x00000216579d2960, 0x00000216579d1c40,
0x00000216579d0200, 0x00000216579d3d10, 0x00000216579d7820, 0x00000216579d4a30,
0x00000216579d43a0, 0x0000021658c3cca0, 0x0000021658c3e110, 0x0000021658c3e7e0,
0x0000021658c3bf00, 0x0000021658c3da40, 0x0000021658c3d370, 0x0000021658c3b830,
0x0000021658c41790
}

Java Threads: ( => current thread )
  0x000002162bde2140 JavaThread "main"                              [_thread_blocked, id=38940, stack(0x000000752c800000,0x000000752c900000) (1024K)]
  0x0000021646461160 JavaThread "Reference Handler"          daemon [_thread_blocked, id=42908, stack(0x000000752d000000,0x000000752d100000) (1024K)]
  0x0000021646463bc0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=26384, stack(0x000000752d100000,0x000000752d200000) (1024K)]
  0x0000021646443d20 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=21212, stack(0x000000752d200000,0x000000752d300000) (1024K)]
  0x00000216464408a0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=24184, stack(0x000000752d300000,0x000000752d400000) (1024K)]
  0x00000216464415c0 JavaThread "Service Thread"             daemon [_thread_blocked, id=31408, stack(0x000000752d400000,0x000000752d500000) (1024K)]
  0x0000021646443000 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=11244, stack(0x000000752d500000,0x000000752d600000) (1024K)]
  0x000002164646efc0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=43272, stack(0x000000752d600000,0x000000752d700000) (1024K)]
  0x0000021646483700 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=37648, stack(0x000000752d700000,0x000000752d800000) (1024K)]
  0x0000021646442970 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=30152, stack(0x000000752d800000,0x000000752d900000) (1024K)]
  0x0000021646441c50 JavaThread "Notification Thread"        daemon [_thread_blocked, id=33208, stack(0x000000752db00000,0x000000752dc00000) (1024K)]
  0x00000216464422e0 JavaThread "Daemon health stats"               [_thread_blocked, id=29548, stack(0x000000752df00000,0x000000752e000000) (1024K)]
  0x0000021646440f30 JavaThread "Incoming local TCP Connector on port 1479"        [_thread_in_native, id=40092, stack(0x000000752ec00000,0x000000752ed00000) (1024K)]
  0x0000021646443690 JavaThread "Daemon periodic checks"            [_thread_blocked, id=31580, stack(0x000000752ed00000,0x000000752ee00000) (1024K)]
  0x000002164c97c6c0 JavaThread "Daemon"                            [_thread_blocked, id=4588, stack(0x000000752ee00000,0x000000752ef00000) (1024K)]
  0x000002164c97da70 JavaThread "Handler for socket connection from /127.0.0.1:1479 to /127.0.0.1:1480"        [_thread_in_native, id=35808, stack(0x000000752ef00000,0x000000752f000000) (1024K)]
  0x000002164c97f4b0 JavaThread "Cancel handler"                    [_thread_blocked, id=33056, stack(0x000000752f000000,0x000000752f100000) (1024K)]
  0x000002164c97ee20 JavaThread "Daemon worker"                     [_thread_in_native, id=38788, stack(0x000000752f100000,0x000000752f200000) (1024K)]
  0x000002164c97cd50 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:1479 to /127.0.0.1:1480"        [_thread_blocked, id=40932, stack(0x000000752f200000,0x000000752f300000) (1024K)]
  0x000002164c980860 JavaThread "Stdin handler"                     [_thread_blocked, id=8884, stack(0x000000752f900000,0x000000752fa00000) (1024K)]
  0x000002164c97d3e0 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=13540, stack(0x000000752fa00000,0x000000752fb00000) (1024K)]
  0x000002164c97e100 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=7172, stack(0x000000752de00000,0x000000752df00000) (1024K)]
  0x000002164c97fb40 JavaThread "File lock request listener"        [_thread_in_native, id=10360, stack(0x000000752fb00000,0x000000752fc00000) (1024K)]
  0x000002164c9801d0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.13\fileHashes)"        [_thread_blocked, id=16888, stack(0x000000752fc00000,0x000000752fd00000) (1024K)]
  0x000002164c980ef0 JavaThread "Cache worker for file hash cache (E:\StudioProjects\AIOSService\.gradle\8.13\fileHashes)"        [_thread_blocked, id=3416, stack(0x000000752fe00000,0x000000752ff00000) (1024K)]
  0x000002164c981580 JavaThread "Cache worker for Build Output Cleanup Cache (E:\StudioProjects\AIOSService\.gradle\buildOutputCleanup)"        [_thread_blocked, id=31440, stack(0x000000752ff00000,0x0000007530000000) (1024K)]
  0x000002164c981c10 JavaThread "Cache worker for Build Output Cleanup Cache (E:\StudioProjects\AIOSService\buildSrc\.gradle\buildOutputCleanup)"        [_thread_blocked, id=15952, stack(0x0000007530000000,0x0000007530100000) (1024K)]
  0x000002164c97e790 JavaThread "File watcher server"        daemon [_thread_in_native, id=34056, stack(0x0000007530100000,0x0000007530200000) (1024K)]
  0x000002164c9822a0 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=24240, stack(0x0000007530200000,0x0000007530300000) (1024K)]
  0x000002164c982930 JavaThread "jar transforms"                    [_thread_blocked, id=35496, stack(0x0000007530300000,0x0000007530400000) (1024K)]
  0x000002164c982fc0 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=17576, stack(0x0000007530400000,0x0000007530500000) (1024K)]
  0x000002164c983650 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=3720, stack(0x0000007530500000,0x0000007530600000) (1024K)]
  0x000002164c983ce0 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=3368, stack(0x0000007530600000,0x0000007530700000) (1024K)]
  0x000002164eb2fe10 JavaThread "jar transforms Thread 5"           [_thread_blocked, id=28540, stack(0x0000007530700000,0x0000007530800000) (1024K)]
  0x000002164eb33290 JavaThread "jar transforms Thread 6"           [_thread_blocked, id=41404, stack(0x0000007530800000,0x0000007530900000) (1024K)]
  0x000002164eb30b30 JavaThread "jar transforms Thread 7"           [_thread_blocked, id=44068, stack(0x0000007530900000,0x0000007530a00000) (1024K)]
  0x000002164eb304a0 JavaThread "jar transforms Thread 8"           [_thread_blocked, id=780, stack(0x0000007530a00000,0x0000007530b00000) (1024K)]
  0x000002164eb311c0 JavaThread "jar transforms Thread 9"           [_thread_blocked, id=30068, stack(0x0000007530b00000,0x0000007530c00000) (1024K)]
  0x000002164eb32570 JavaThread "jar transforms Thread 10"          [_thread_blocked, id=23452, stack(0x0000007530c00000,0x0000007530d00000) (1024K)]
  0x000002164eb31850 JavaThread "jar transforms Thread 11"          [_thread_blocked, id=39576, stack(0x0000007530d00000,0x0000007530e00000) (1024K)]
  0x000002164eb31ee0 JavaThread "jar transforms Thread 12"          [_thread_blocked, id=37868, stack(0x0000007530e00000,0x0000007530f00000) (1024K)]
  0x000002164eb32c00 JavaThread "jar transforms Thread 13"          [_thread_blocked, id=39816, stack(0x0000007530f00000,0x0000007531000000) (1024K)]
  0x000002164dfb5ab0 JavaThread "jar transforms Thread 14"          [_thread_blocked, id=38560, stack(0x0000007531000000,0x0000007531100000) (1024K)]
  0x000002164dfb8210 JavaThread "jar transforms Thread 15"          [_thread_blocked, id=17880, stack(0x0000007531100000,0x0000007531200000) (1024K)]
  0x000002164dfb74f0 JavaThread "jar transforms Thread 16"          [_thread_blocked, id=29892, stack(0x0000007531200000,0x0000007531300000) (1024K)]
  0x000002164dfb6140 JavaThread "jar transforms Thread 17"          [_thread_blocked, id=4592, stack(0x0000007531300000,0x0000007531400000) (1024K)]
  0x000002164dfb67d0 JavaThread "jar transforms Thread 18"          [_thread_blocked, id=30288, stack(0x0000007531400000,0x0000007531500000) (1024K)]
  0x000002164dfb6e60 JavaThread "jar transforms Thread 19"          [_thread_blocked, id=4288, stack(0x0000007531500000,0x0000007531600000) (1024K)]
  0x000002164dfb7b80 JavaThread "jar transforms Thread 20"          [_thread_blocked, id=36440, stack(0x0000007531600000,0x0000007531700000) (1024K)]
  0x000002164dfb5420 JavaThread "Cache worker for checksums cache (E:\StudioProjects\AIOSService\.gradle\8.13\checksums)"        [_thread_blocked, id=33280, stack(0x0000007531700000,0x0000007531800000) (1024K)]
  0x000002164dfba970 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.13\fileContent)"        [_thread_blocked, id=11056, stack(0x0000007531800000,0x0000007531900000) (1024K)]
  0x000002164dfbbd20 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.13\md-supplier)"        [_thread_blocked, id=35640, stack(0x0000007531900000,0x0000007531a00000) (1024K)]
  0x000002164dfbc3b0 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.13\md-rule)"        [_thread_blocked, id=44680, stack(0x0000007531a00000,0x0000007531b00000) (1024K)]
  0x000002164dfb8f30 JavaThread "Unconstrained build operations"        [_thread_blocked, id=6732, stack(0x0000007531b00000,0x0000007531c00000) (1024K)]
  0x000002164dfb95c0 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=38360, stack(0x0000007531c00000,0x0000007531d00000) (1024K)]
  0x000002164dfbb000 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=35564, stack(0x0000007531d00000,0x0000007531e00000) (1024K)]
  0x000002164dfbb690 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=18304, stack(0x0000007531e00000,0x0000007531f00000) (1024K)]
  0x000002165148ae40 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=17380, stack(0x0000007531f00000,0x0000007532000000) (1024K)]
  0x000002165148b4d0 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=44324, stack(0x0000007532000000,0x0000007532100000) (1024K)]
  0x0000021651488050 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=29352, stack(0x0000007532100000,0x0000007532200000) (1024K)]
  0x000002165148a120 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=34208, stack(0x0000007532200000,0x0000007532300000) (1024K)]
  0x00000216514886e0 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=20032, stack(0x0000007532300000,0x0000007532400000) (1024K)]
  0x0000021651489400 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=8396, stack(0x0000007532400000,0x0000007532500000) (1024K)]
  0x0000021651488d70 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=26052, stack(0x0000007532500000,0x0000007532600000) (1024K)]
  0x000002165148bb60 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=32028, stack(0x0000007532600000,0x0000007532700000) (1024K)]
  0x000002165148c1f0 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=24236, stack(0x0000007532700000,0x0000007532800000) (1024K)]
  0x000002165148c880 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=20128, stack(0x0000007532800000,0x0000007532900000) (1024K)]
  0x000002165148cf10 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=32448, stack(0x0000007532900000,0x0000007532a00000) (1024K)]
  0x000002165148a7b0 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=34544, stack(0x0000007532a00000,0x0000007532b00000) (1024K)]
  0x0000021651489a90 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=41948, stack(0x0000007532b00000,0x0000007532c00000) (1024K)]
  0x000002165148dc30 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=14296, stack(0x0000007532c00000,0x0000007532d00000) (1024K)]
  0x000002165148e2c0 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=17180, stack(0x0000007532d00000,0x0000007532e00000) (1024K)]
  0x000002165148d5a0 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=32972, stack(0x0000007532e00000,0x0000007532f00000) (1024K)]
  0x000002165148efe0 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=27392, stack(0x0000007532f00000,0x0000007533000000) (1024K)]
  0x000002165148e950 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=9564, stack(0x0000007533000000,0x0000007533100000) (1024K)]
  0x000002165148f670 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=31988, stack(0x0000007533100000,0x0000007533200000) (1024K)]
  0x00000216515f2760 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=18444, stack(0x0000007533200000,0x0000007533300000) (1024K)]
  0x00000216515f3480 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=23548, stack(0x0000007533300000,0x0000007533400000) (1024K)]
  0x00000216515f2df0 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=41920, stack(0x0000007533400000,0x0000007533500000) (1024K)]
  0x00000216515f3b10 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=30972, stack(0x0000007533500000,0x0000007533600000) (1024K)]
  0x00000216515f13b0 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=39832, stack(0x0000007533600000,0x0000007533700000) (1024K)]
  0x00000216515f41a0 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=40348, stack(0x0000007533700000,0x0000007533800000) (1024K)]
  0x00000216515f0d20 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=43700, stack(0x0000007533800000,0x0000007533900000) (1024K)]
  0x00000216515f4830 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=44852, stack(0x0000007533900000,0x0000007533a00000) (1024K)]
  0x00000216515f4ec0 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=27868, stack(0x0000007533a00000,0x0000007533b00000) (1024K)]
  0x00000216515f1a40 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=38860, stack(0x0000007533b00000,0x0000007533c00000) (1024K)]
  0x00000216515f5be0 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=18644, stack(0x0000007533c00000,0x0000007533d00000) (1024K)]
  0x00000216515f7620 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=26468, stack(0x0000007533d00000,0x0000007533e00000) (1024K)]
  0x00000216515f5550 JavaThread "Memory manager"                    [_thread_blocked, id=27324, stack(0x000000752fd00000,0x000000752fe00000) (1024K)]
  0x00000216515f6270 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=6216, stack(0x0000007533e00000,0x0000007533f00000) (1024K)]
  0x00000216515f6900 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=16436, stack(0x0000007533f00000,0x0000007534000000) (1024K)]
  0x000002164ba284a0 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=37260, stack(0x0000007534000000,0x0000007534100000) (1024K)]
  0x000002164ba27e10 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=28504, stack(0x0000007534100000,0x0000007534200000) (1024K)]
  0x000002164ba29850 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=18688, stack(0x0000007534200000,0x0000007534300000) (1024K)]
  0x000002164ba291c0 JavaThread "Unconstrained build operations Thread 41"        [_thread_blocked, id=17852, stack(0x0000007534300000,0x0000007534400000) (1024K)]
  0x000002164ba263d0 JavaThread "Unconstrained build operations Thread 42"        [_thread_blocked, id=29048, stack(0x0000007534400000,0x0000007534500000) (1024K)]
  0x000002164ba29ee0 JavaThread "Unconstrained build operations Thread 43"        [_thread_blocked, id=42064, stack(0x0000007534500000,0x0000007534600000) (1024K)]
  0x000002164ba26a60 JavaThread "Unconstrained build operations Thread 44"        [_thread_blocked, id=36884, stack(0x0000007534600000,0x0000007534700000) (1024K)]
  0x000002164ba2a570 JavaThread "Unconstrained build operations Thread 45"        [_thread_blocked, id=18536, stack(0x0000007534700000,0x0000007534800000) (1024K)]
  0x000002164ba270f0 JavaThread "Unconstrained build operations Thread 46"        [_thread_blocked, id=8040, stack(0x0000007534800000,0x0000007534900000) (1024K)]
  0x000002164ba28b30 JavaThread "Unconstrained build operations Thread 47"        [_thread_blocked, id=42608, stack(0x0000007534900000,0x0000007534a00000) (1024K)]
  0x000002164ba2ac00 JavaThread "Unconstrained build operations Thread 48"        [_thread_blocked, id=35616, stack(0x0000007534a00000,0x0000007534b00000) (1024K)]
  0x000002164ba27780 JavaThread "Unconstrained build operations Thread 49"        [_thread_blocked, id=18136, stack(0x0000007534b00000,0x0000007534c00000) (1024K)]
  0x000002164ba2c640 JavaThread "Unconstrained build operations Thread 50"        [_thread_blocked, id=44988, stack(0x0000007534c00000,0x0000007534d00000) (1024K)]
  0x000002164ba2b290 JavaThread "Unconstrained build operations Thread 51"        [_thread_blocked, id=26684, stack(0x0000007534d00000,0x0000007534e00000) (1024K)]
  0x000002164ba2d9f0 JavaThread "Unconstrained build operations Thread 52"        [_thread_blocked, id=2372, stack(0x0000007534e00000,0x0000007534f00000) (1024K)]
  0x000002164ba2ccd0 JavaThread "Unconstrained build operations Thread 53"        [_thread_blocked, id=31084, stack(0x0000007534f00000,0x0000007535000000) (1024K)]
  0x000002164ba2b920 JavaThread "Unconstrained build operations Thread 54"        [_thread_blocked, id=27176, stack(0x0000007535000000,0x0000007535100000) (1024K)]
  0x000002164ba2d360 JavaThread "Unconstrained build operations Thread 55"        [_thread_blocked, id=33624, stack(0x0000007535100000,0x0000007535200000) (1024K)]
  0x000002164ba2bfb0 JavaThread "Unconstrained build operations Thread 56"        [_thread_blocked, id=38456, stack(0x0000007535200000,0x0000007535300000) (1024K)]
  0x000002164f8efee0 JavaThread "Unconstrained build operations Thread 57"        [_thread_blocked, id=36228, stack(0x0000007535300000,0x0000007535400000) (1024K)]
  0x000002164f8ef1c0 JavaThread "Unconstrained build operations Thread 58"        [_thread_blocked, id=44572, stack(0x0000007535400000,0x0000007535500000) (1024K)]
  0x000002164f8f0570 JavaThread "Unconstrained build operations Thread 59"        [_thread_blocked, id=15412, stack(0x0000007535500000,0x0000007535600000) (1024K)]
  0x000002164f8f1290 JavaThread "Unconstrained build operations Thread 60"        [_thread_blocked, id=28708, stack(0x0000007535600000,0x0000007535700000) (1024K)]
  0x000002164f8f1920 JavaThread "Unconstrained build operations Thread 61"        [_thread_blocked, id=7508, stack(0x0000007535700000,0x0000007535800000) (1024K)]
  0x000002164f8ee4a0 JavaThread "Unconstrained build operations Thread 62"        [_thread_blocked, id=21720, stack(0x0000007535800000,0x0000007535900000) (1024K)]
  0x000002164f8ef850 JavaThread "Unconstrained build operations Thread 63"        [_thread_blocked, id=36136, stack(0x0000007535900000,0x0000007535a00000) (1024K)]
  0x000002164f8eeb30 JavaThread "Unconstrained build operations Thread 64"        [_thread_blocked, id=39364, stack(0x0000007535a00000,0x0000007535b00000) (1024K)]
  0x000002164f8f2cd0 JavaThread "Unconstrained build operations Thread 65"        [_thread_blocked, id=31296, stack(0x0000007535b00000,0x0000007535c00000) (1024K)]
  0x000002164f8f3360 JavaThread "Unconstrained build operations Thread 66"        [_thread_blocked, id=34904, stack(0x0000007535c00000,0x0000007535d00000) (1024K)]
  0x000002164f8f39f0 JavaThread "Unconstrained build operations Thread 67"        [_thread_blocked, id=42556, stack(0x0000007535d00000,0x0000007535e00000) (1024K)]
  0x000002164f8f4080 JavaThread "Unconstrained build operations Thread 68"        [_thread_blocked, id=44120, stack(0x0000007535e00000,0x0000007535f00000) (1024K)]
  0x000002164f8f0c00 JavaThread "Unconstrained build operations Thread 69"        [_thread_blocked, id=37164, stack(0x0000007535f00000,0x0000007536000000) (1024K)]
  0x000002164f8f4710 JavaThread "Unconstrained build operations Thread 70"        [_thread_blocked, id=43252, stack(0x0000007536000000,0x0000007536100000) (1024K)]
  0x000002164f8f4da0 JavaThread "Unconstrained build operations Thread 71"        [_thread_blocked, id=27928, stack(0x0000007536100000,0x0000007536200000) (1024K)]
  0x000002164f8f1fb0 JavaThread "Unconstrained build operations Thread 72"        [_thread_blocked, id=31804, stack(0x0000007536200000,0x0000007536300000) (1024K)]
  0x000002164f8f5430 JavaThread "Unconstrained build operations Thread 73"        [_thread_blocked, id=32728, stack(0x0000007536300000,0x0000007536400000) (1024K)]
  0x000002164f8f5ac0 JavaThread "Unconstrained build operations Thread 74"        [_thread_blocked, id=20928, stack(0x0000007536400000,0x0000007536500000) (1024K)]
  0x000002164f8f2640 JavaThread "Unconstrained build operations Thread 75"        [_thread_blocked, id=30800, stack(0x0000007536500000,0x0000007536600000) (1024K)]
  0x000002164fea5740 JavaThread "Unconstrained build operations Thread 76"        [_thread_blocked, id=25496, stack(0x0000007536600000,0x0000007536700000) (1024K)]
  0x000002164fea7180 JavaThread "Unconstrained build operations Thread 77"        [_thread_blocked, id=43796, stack(0x0000007536700000,0x0000007536800000) (1024K)]
  0x000002164fea6af0 JavaThread "Unconstrained build operations Thread 78"        [_thread_blocked, id=29240, stack(0x0000007536800000,0x0000007536900000) (1024K)]
  0x000002164fea5dd0 JavaThread "Unconstrained build operations Thread 79"        [_thread_blocked, id=24020, stack(0x0000007536900000,0x0000007536a00000) (1024K)]
  0x000002164fea4a20 JavaThread "Unconstrained build operations Thread 80"        [_thread_blocked, id=23100, stack(0x0000007536a00000,0x0000007536b00000) (1024K)]
  0x000002164fea7810 JavaThread "Unconstrained build operations Thread 81"        [_thread_blocked, id=17344, stack(0x0000007536b00000,0x0000007536c00000) (1024K)]
  0x000002164fea50b0 JavaThread "Unconstrained build operations Thread 82"        [_thread_blocked, id=29440, stack(0x0000007536c00000,0x0000007536d00000) (1024K)]
  0x000002164fea6460 JavaThread "Unconstrained build operations Thread 83"        [_thread_blocked, id=27716, stack(0x0000007536d00000,0x0000007536e00000) (1024K)]
  0x000002164fea4390 JavaThread "Unconstrained build operations Thread 84"        [_thread_blocked, id=41184, stack(0x0000007536e00000,0x0000007536f00000) (1024K)]
  0x000002164feaac90 JavaThread "Unconstrained build operations Thread 85"        [_thread_blocked, id=18960, stack(0x0000007536f00000,0x0000007537000000) (1024K)]
  0x000002164feab320 JavaThread "Unconstrained build operations Thread 86"        [_thread_blocked, id=2476, stack(0x0000007537000000,0x0000007537100000) (1024K)]
  0x000002164fea98e0 JavaThread "Unconstrained build operations Thread 87"        [_thread_blocked, id=29824, stack(0x0000007537100000,0x0000007537200000) (1024K)]
  0x000002164fea7ea0 JavaThread "Unconstrained build operations Thread 88"        [_thread_blocked, id=3812, stack(0x0000007537200000,0x0000007537300000) (1024K)]
  0x000002164fea9f70 JavaThread "Unconstrained build operations Thread 89"        [_thread_blocked, id=34684, stack(0x0000007537300000,0x0000007537400000) (1024K)]
  0x000002164fea9250 JavaThread "Unconstrained build operations Thread 90"        [_thread_blocked, id=7380, stack(0x0000007537400000,0x0000007537500000) (1024K)]
  0x000002164feab9b0 JavaThread "Unconstrained build operations Thread 91"        [_thread_blocked, id=10220, stack(0x0000007537500000,0x0000007537600000) (1024K)]
  0x000002164fea8530 JavaThread "Unconstrained build operations Thread 92"        [_thread_blocked, id=39592, stack(0x0000007537600000,0x0000007537700000) (1024K)]
  0x0000021651aae0d0 JavaThread "build event listener"              [_thread_blocked, id=36060, stack(0x0000007537800000,0x0000007537900000) (1024K)]
  0x0000021651ab01a0 JavaThread "Execution worker"                  [_thread_blocked, id=41356, stack(0x0000007537a00000,0x0000007537b00000) (1024K)]
  0x0000021651aacd20 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=21624, stack(0x0000007537b00000,0x0000007537c00000) (1024K)]
  0x0000021651ab0830 JavaThread "Execution worker Thread 3"         [_thread_blocked, id=29484, stack(0x0000007537c00000,0x0000007537d00000) (1024K)]
  0x0000021651aad3b0 JavaThread "Execution worker Thread 4"         [_thread_blocked, id=13084, stack(0x0000007537d00000,0x0000007537e00000) (1024K)]
  0x0000021651ab0ec0 JavaThread "Execution worker Thread 5"         [_thread_blocked, id=17844, stack(0x0000007537e00000,0x0000007537f00000) (1024K)]
  0x0000021651ab1be0 JavaThread "Execution worker Thread 6"         [_thread_blocked, id=28428, stack(0x0000007537f00000,0x0000007538000000) (1024K)]
  0x0000021651ab1550 JavaThread "Execution worker Thread 7"         [_thread_blocked, id=42188, stack(0x0000007538000000,0x0000007538100000) (1024K)]
  0x0000021651ab2f90 JavaThread "Execution worker Thread 8"         [_thread_blocked, id=13592, stack(0x0000007538100000,0x0000007538200000) (1024K)]
  0x0000021651ab2270 JavaThread "Execution worker Thread 9"         [_thread_blocked, id=27796, stack(0x0000007538200000,0x0000007538300000) (1024K)]
  0x0000021651ab2900 JavaThread "Execution worker Thread 10"        [_thread_blocked, id=32892, stack(0x0000007538300000,0x0000007538400000) (1024K)]
  0x0000021651ab3620 JavaThread "Execution worker Thread 11"        [_thread_blocked, id=44216, stack(0x0000007538400000,0x0000007538500000) (1024K)]
  0x0000021646d30c90 JavaThread "Execution worker Thread 12"        [_thread_blocked, id=27280, stack(0x0000007538500000,0x0000007538600000) (1024K)]
  0x0000021646d2f8e0 JavaThread "Execution worker Thread 13"        [_thread_blocked, id=8404, stack(0x0000007538600000,0x0000007538700000) (1024K)]
  0x0000021646d31320 JavaThread "Execution worker Thread 14"        [_thread_blocked, id=33536, stack(0x0000007538700000,0x0000007538800000) (1024K)]
  0x0000021646d333f0 JavaThread "Execution worker Thread 15"        [_thread_blocked, id=33196, stack(0x0000007538800000,0x0000007538900000) (1024K)]
  0x0000021646d326d0 JavaThread "Execution worker Thread 16"        [_thread_blocked, id=43936, stack(0x0000007538900000,0x0000007538a00000) (1024K)]
  0x0000021646d2ff70 JavaThread "Execution worker Thread 17"        [_thread_blocked, id=32700, stack(0x0000007538a00000,0x0000007538b00000) (1024K)]
  0x0000021646d30600 JavaThread "Execution worker Thread 18"        [_thread_blocked, id=42844, stack(0x0000007538b00000,0x0000007538c00000) (1024K)]
  0x0000021646d33a80 JavaThread "Execution worker Thread 19"        [_thread_blocked, id=42040, stack(0x0000007538c00000,0x0000007538d00000) (1024K)]
  0x0000021646d32d60 JavaThread "Cache worker for execution history cache (E:\StudioProjects\AIOSService\buildSrc\.gradle\8.13\executionHistory)"        [_thread_blocked, id=43624, stack(0x0000007538d00000,0x0000007538e00000) (1024K)]
  0x0000021646d32040 JavaThread "Unconstrained build operations Thread 93"        [_thread_blocked, id=16244, stack(0x0000007538e00000,0x0000007538f00000) (1024K)]
  0x0000021646d34110 JavaThread "Unconstrained build operations Thread 94"        [_thread_blocked, id=876, stack(0x0000007538f00000,0x0000007539000000) (1024K)]
  0x0000021646d347a0 JavaThread "Unconstrained build operations Thread 95"        [_thread_blocked, id=9648, stack(0x0000007539000000,0x0000007539100000) (1024K)]
  0x0000021646d34e30 JavaThread "Unconstrained build operations Thread 96"        [_thread_blocked, id=24284, stack(0x0000007539100000,0x0000007539200000) (1024K)]
  0x0000021646d319b0 JavaThread "Unconstrained build operations Thread 97"        [_thread_blocked, id=852, stack(0x0000007539200000,0x0000007539300000) (1024K)]
  0x0000021646d361e0 JavaThread "Unconstrained build operations Thread 98"        [_thread_blocked, id=41140, stack(0x0000007539300000,0x0000007539400000) (1024K)]
  0x0000021646d36870 JavaThread "Unconstrained build operations Thread 99"        [_thread_blocked, id=33164, stack(0x0000007539400000,0x0000007539500000) (1024K)]
  0x0000021646d382b0 JavaThread "Unconstrained build operations Thread 100"        [_thread_blocked, id=43812, stack(0x0000007539500000,0x0000007539600000) (1024K)]
  0x0000021646d36f00 JavaThread "Unconstrained build operations Thread 101"        [_thread_blocked, id=27160, stack(0x0000007539600000,0x0000007539700000) (1024K)]
  0x0000021646d35b50 JavaThread "Unconstrained build operations Thread 102"        [_thread_blocked, id=25404, stack(0x0000007539700000,0x0000007539800000) (1024K)]
  0x0000021646d37590 JavaThread "Unconstrained build operations Thread 103"        [_thread_blocked, id=19556, stack(0x0000007539800000,0x0000007539900000) (1024K)]
  0x0000021646d354c0 JavaThread "Unconstrained build operations Thread 104"        [_thread_blocked, id=13860, stack(0x0000007539900000,0x0000007539a00000) (1024K)]
  0x0000021646d3b0a0 JavaThread "Unconstrained build operations Thread 105"        [_thread_blocked, id=35756, stack(0x0000007539a00000,0x0000007539b00000) (1024K)]
  0x0000021646d39cf0 JavaThread "Unconstrained build operations Thread 106"        [_thread_blocked, id=15032, stack(0x0000007539b00000,0x0000007539c00000) (1024K)]
  0x0000021646d37c20 JavaThread "Unconstrained build operations Thread 107"        [_thread_blocked, id=20980, stack(0x0000007539c00000,0x0000007539d00000) (1024K)]
  0x0000021646d38fd0 JavaThread "Unconstrained build operations Thread 108"        [_thread_blocked, id=9012, stack(0x0000007539d00000,0x0000007539e00000) (1024K)]
  0x0000021646d39660 JavaThread "Unconstrained build operations Thread 109"        [_thread_blocked, id=20104, stack(0x0000007539e00000,0x0000007539f00000) (1024K)]
  0x0000021646d3bdc0 JavaThread "Unconstrained build operations Thread 110"        [_thread_blocked, id=39540, stack(0x0000007539f00000,0x000000753a000000) (1024K)]
  0x0000021646d3b730 JavaThread "Unconstrained build operations Thread 111"        [_thread_blocked, id=44824, stack(0x000000753a000000,0x000000753a100000) (1024K)]
  0x0000021646d38940 JavaThread "Unconstrained build operations Thread 112"        [_thread_blocked, id=44596, stack(0x000000753a100000,0x000000753a200000) (1024K)]
  0x0000021646d3d170 JavaThread "Unconstrained build operations Thread 113"        [_thread_blocked, id=43584, stack(0x000000753a200000,0x000000753a300000) (1024K)]
  0x0000021646d3c450 JavaThread "Unconstrained build operations Thread 114"        [_thread_blocked, id=12248, stack(0x000000753a300000,0x000000753a400000) (1024K)]
  0x0000021646d3aa10 JavaThread "Unconstrained build operations Thread 115"        [_thread_blocked, id=27128, stack(0x000000753a400000,0x000000753a500000) (1024K)]
  0x0000021646d3d800 JavaThread "Unconstrained build operations Thread 116"        [_thread_blocked, id=16208, stack(0x000000753a500000,0x000000753a600000) (1024K)]
  0x0000021646d3cae0 JavaThread "Unconstrained build operations Thread 117"        [_thread_blocked, id=25016, stack(0x000000753a600000,0x000000753a700000) (1024K)]
  0x0000021646d3de90 JavaThread "Unconstrained build operations Thread 118"        [_thread_blocked, id=32996, stack(0x000000753a700000,0x000000753a800000) (1024K)]
  0x0000021646d3e520 JavaThread "Unconstrained build operations Thread 119"        [_thread_blocked, id=18252, stack(0x000000753a800000,0x000000753a900000) (1024K)]
  0x0000021650213ef0 JavaThread "Unconstrained build operations Thread 120"        [_thread_blocked, id=43388, stack(0x000000753a900000,0x000000753aa00000) (1024K)]
  0x0000021650214580 JavaThread "Unconstrained build operations Thread 121"        [_thread_blocked, id=32748, stack(0x000000753aa00000,0x000000753ab00000) (1024K)]
  0x0000021650211e20 JavaThread "Unconstrained build operations Thread 122"        [_thread_blocked, id=16716, stack(0x000000753ab00000,0x000000753ac00000) (1024K)]
  0x00000216502152a0 JavaThread "Unconstrained build operations Thread 123"        [_thread_blocked, id=38220, stack(0x000000753ac00000,0x000000753ad00000) (1024K)]
  0x0000021650215930 JavaThread "Unconstrained build operations Thread 124"        [_thread_blocked, id=10488, stack(0x000000753ad00000,0x000000753ae00000) (1024K)]
  0x00000216502124b0 JavaThread "Unconstrained build operations Thread 125"        [_thread_blocked, id=38876, stack(0x000000753ae00000,0x000000753af00000) (1024K)]
  0x0000021650213860 JavaThread "Unconstrained build operations Thread 126"        [_thread_blocked, id=29736, stack(0x000000753af00000,0x000000753b000000) (1024K)]
  0x0000021650212b40 JavaThread "Unconstrained build operations Thread 127"        [_thread_blocked, id=40652, stack(0x000000753b000000,0x000000753b100000) (1024K)]
  0x0000021650214c10 JavaThread "Unconstrained build operations Thread 128"        [_thread_blocked, id=18408, stack(0x000000753b100000,0x000000753b200000) (1024K)]
  0x0000021650215fc0 JavaThread "Unconstrained build operations Thread 129"        [_thread_blocked, id=40908, stack(0x000000753b200000,0x000000753b300000) (1024K)]
  0x00000216502131d0 JavaThread "Unconstrained build operations Thread 130"        [_thread_blocked, id=21072, stack(0x000000753b300000,0x000000753b400000) (1024K)]
  0x0000021650217a00 JavaThread "Unconstrained build operations Thread 131"        [_thread_blocked, id=32584, stack(0x000000753b400000,0x000000753b500000) (1024K)]
  0x0000021650216ce0 JavaThread "WorkerExecutor Queue"              [_thread_blocked, id=39144, stack(0x000000753b500000,0x000000753b600000) (1024K)]
  0x0000021650217370 JavaThread "RMI Scheduler(0)"           daemon [_thread_blocked, id=36116, stack(0x000000753b600000,0x000000753b700000) (1024K)]
  0x0000021646d3ebb0 JavaThread "RMI TCP Accept-0"           daemon [_thread_in_native, id=35056, stack(0x000000753b900000,0x000000753ba00000) (1024K)]
  0x000002164d984d20 JavaThread "Problems report writer"            [_thread_blocked, id=43332, stack(0x0000007537700000,0x0000007537800000) (1024K)]
  0x000002164d989be0 JavaThread "Unconstrained build operations Thread 132"        [_thread_blocked, id=43480, stack(0x000000753b700000,0x000000753b800000) (1024K)]
  0x000002164d986760 JavaThread "Unconstrained build operations Thread 133"        [_thread_blocked, id=25788, stack(0x000000753bc00000,0x000000753bd00000) (1024K)]
  0x000002164d98b620 JavaThread "Unconstrained build operations Thread 134"        [_thread_blocked, id=6272, stack(0x000000753bd00000,0x000000753be00000) (1024K)]
  0x000002164d98d6f0 JavaThread "Unconstrained build operations Thread 135"        [_thread_blocked, id=32076, stack(0x000000753be00000,0x000000753bf00000) (1024K)]
  0x000002164d98af90 JavaThread "Unconstrained build operations Thread 136"        [_thread_blocked, id=18024, stack(0x000000753bf00000,0x000000753c000000) (1024K)]
  0x000002164d98a270 JavaThread "Unconstrained build operations Thread 137"        [_thread_blocked, id=38112, stack(0x000000753c000000,0x000000753c100000) (1024K)]
  0x000002164d98d060 JavaThread "Unconstrained build operations Thread 138"        [_thread_blocked, id=33240, stack(0x000000753c100000,0x000000753c200000) (1024K)]
  0x000002164d98c9d0 JavaThread "Unconstrained build operations Thread 139"        [_thread_blocked, id=16124, stack(0x000000753c200000,0x000000753c300000) (1024K)]
  0x000002164d98bcb0 JavaThread "Unconstrained build operations Thread 140"        [_thread_blocked, id=17656, stack(0x000000753c300000,0x000000753c400000) (1024K)]
  0x000002164d98dd80 JavaThread "Unconstrained build operations Thread 141"        [_thread_blocked, id=15436, stack(0x000000753c600000,0x000000753c700000) (1024K)]
  0x000002164d98c340 JavaThread "Unconstrained build operations Thread 142"        [_thread_blocked, id=23024, stack(0x000000753c700000,0x000000753c800000) (1024K)]
  0x000002164d98a900 JavaThread "Unconstrained build operations Thread 143"        [_thread_blocked, id=43868, stack(0x000000753c800000,0x000000753c900000) (1024K)]
  0x000002164d98f7c0 JavaThread "Unconstrained build operations Thread 144"        [_thread_blocked, id=41072, stack(0x000000753c900000,0x000000753ca00000) (1024K)]
  0x000002164d9904e0 JavaThread "Unconstrained build operations Thread 145"        [_thread_blocked, id=44500, stack(0x000000753ca00000,0x000000753cb00000) (1024K)]
  0x000002164d98eaa0 JavaThread "Unconstrained build operations Thread 146"        [_thread_blocked, id=37744, stack(0x000000753cb00000,0x000000753cc00000) (1024K)]
  0x000002164d990b70 JavaThread "Unconstrained build operations Thread 147"        [_thread_blocked, id=38612, stack(0x000000753cc00000,0x000000753cd00000) (1024K)]
  0x000002164d991200 JavaThread "Unconstrained build operations Thread 148"        [_thread_blocked, id=25636, stack(0x000000753cd00000,0x000000753ce00000) (1024K)]
  0x000002164d98e410 JavaThread "Unconstrained build operations Thread 149"        [_thread_blocked, id=17228, stack(0x000000753ce00000,0x000000753cf00000) (1024K)]
  0x000002164d98f130 JavaThread "Unconstrained build operations Thread 150"        [_thread_blocked, id=33540, stack(0x000000753cf00000,0x000000753d000000) (1024K)]
  0x000002164d991890 JavaThread "Unconstrained build operations Thread 151"        [_thread_blocked, id=3940, stack(0x000000753d000000,0x000000753d100000) (1024K)]
  0x000002164d991f20 JavaThread "Unconstrained build operations Thread 152"        [_thread_blocked, id=34224, stack(0x000000753d100000,0x000000753d200000) (1024K)]
  0x000002164d9925b0 JavaThread "Unconstrained build operations Thread 153"        [_thread_blocked, id=29092, stack(0x000000753d200000,0x000000753d300000) (1024K)]
  0x000002164d992c40 JavaThread "Unconstrained build operations Thread 154"        [_thread_blocked, id=15852, stack(0x000000753d300000,0x000000753d400000) (1024K)]
  0x000002164d98fe50 JavaThread "Unconstrained build operations Thread 155"        [_thread_blocked, id=3304, stack(0x000000753d400000,0x000000753d500000) (1024K)]
  0x0000021650218090 JavaThread "Unconstrained build operations Thread 156"        [_thread_blocked, id=42204, stack(0x000000753d500000,0x000000753d600000) (1024K)]
  0x0000021651293d10 JavaThread "Unconstrained build operations Thread 157"        [_thread_blocked, id=9732, stack(0x000000753d600000,0x000000753d700000) (1024K)]
  0x00000216512943a0 JavaThread "Unconstrained build operations Thread 158"        [_thread_blocked, id=18076, stack(0x000000753d700000,0x000000753d800000) (1024K)]
  0x00000216512915b0 JavaThread "Unconstrained build operations Thread 159"        [_thread_blocked, id=12684, stack(0x000000753d800000,0x000000753d900000) (1024K)]
  0x0000021651294a30 JavaThread "Unconstrained build operations Thread 160"        [_thread_blocked, id=1776, stack(0x000000753d900000,0x000000753da00000) (1024K)]
  0x0000021651292960 JavaThread "Unconstrained build operations Thread 161"        [_thread_blocked, id=16636, stack(0x000000753da00000,0x000000753db00000) (1024K)]
  0x0000021651293680 JavaThread "Unconstrained build operations Thread 162"        [_thread_blocked, id=32716, stack(0x000000753db00000,0x000000753dc00000) (1024K)]
  0x00000216512950c0 JavaThread "Unconstrained build operations Thread 163"        [_thread_blocked, id=38004, stack(0x000000753dc00000,0x000000753dd00000) (1024K)]
  0x0000021651292ff0 JavaThread "Unconstrained build operations Thread 164"        [_thread_blocked, id=43568, stack(0x000000753dd00000,0x000000753de00000) (1024K)]
  0x0000021651291c40 JavaThread "Unconstrained build operations Thread 165"        [_thread_blocked, id=29536, stack(0x000000753de00000,0x000000753df00000) (1024K)]
  0x0000021651295750 JavaThread "Unconstrained build operations Thread 166"        [_thread_blocked, id=34948, stack(0x000000753df00000,0x000000753e000000) (1024K)]
  0x00000216512922d0 JavaThread "Unconstrained build operations Thread 167"        [_thread_blocked, id=41016, stack(0x000000753e000000,0x000000753e100000) (1024K)]
  0x0000021651295de0 JavaThread "Unconstrained build operations Thread 168"        [_thread_blocked, id=2240, stack(0x000000753e100000,0x000000753e200000) (1024K)]
  0x0000021651299260 JavaThread "Unconstrained build operations Thread 169"        [_thread_blocked, id=32088, stack(0x000000753e200000,0x000000753e300000) (1024K)]
  0x0000021651296b00 JavaThread "Unconstrained build operations Thread 170"        [_thread_blocked, id=24972, stack(0x000000753e300000,0x000000753e400000) (1024K)]
  0x0000021651297190 JavaThread "Unconstrained build operations Thread 171"        [_thread_blocked, id=35832, stack(0x000000753e400000,0x000000753e500000) (1024K)]
  0x0000021651296470 JavaThread "Unconstrained build operations Thread 172"        [_thread_blocked, id=34832, stack(0x000000753e500000,0x000000753e600000) (1024K)]
  0x00000216512998f0 JavaThread "Unconstrained build operations Thread 173"        [_thread_blocked, id=32424, stack(0x000000753e600000,0x000000753e700000) (1024K)]
  0x000002165129aca0 JavaThread "Unconstrained build operations Thread 174"        [_thread_blocked, id=8260, stack(0x000000753e700000,0x000000753e800000) (1024K)]
  0x0000021651299f80 JavaThread "Unconstrained build operations Thread 175"        [_thread_blocked, id=700, stack(0x000000753e800000,0x000000753e900000) (1024K)]
  0x0000021651297820 JavaThread "Unconstrained build operations Thread 176"        [_thread_blocked, id=13936, stack(0x000000753e900000,0x000000753ea00000) (1024K)]
  0x000002165129b330 JavaThread "Unconstrained build operations Thread 177"        [_thread_blocked, id=34284, stack(0x000000753ea00000,0x000000753eb00000) (1024K)]
  0x0000021651297eb0 JavaThread "Unconstrained build operations Thread 178"        [_thread_blocked, id=11816, stack(0x000000753eb00000,0x000000753ec00000) (1024K)]
  0x000002165129b9c0 JavaThread "Unconstrained build operations Thread 179"        [_thread_blocked, id=19536, stack(0x000000753ec00000,0x000000753ed00000) (1024K)]
  0x000002165129a610 JavaThread "Unconstrained build operations Thread 180"        [_thread_blocked, id=44600, stack(0x000000753ed00000,0x000000753ee00000) (1024K)]
  0x0000021651298bd0 JavaThread "Unconstrained build operations Thread 181"        [_thread_blocked, id=40316, stack(0x000000753ee00000,0x000000753ef00000) (1024K)]
  0x0000021651298540 JavaThread "Unconstrained build operations Thread 182"        [_thread_blocked, id=33608, stack(0x000000753ef00000,0x000000753f000000) (1024K)]
  0x000002165129d400 JavaThread "Unconstrained build operations Thread 183"        [_thread_blocked, id=30644, stack(0x000000753f000000,0x000000753f100000) (1024K)]
  0x000002165129da90 JavaThread "Unconstrained build operations Thread 184"        [_thread_blocked, id=21064, stack(0x000000753f100000,0x000000753f200000) (1024K)]
  0x000002165129e120 JavaThread "Unconstrained build operations Thread 185"        [_thread_blocked, id=36368, stack(0x000000753f200000,0x000000753f300000) (1024K)]
  0x000002165129ee40 JavaThread "Unconstrained build operations Thread 186"        [_thread_blocked, id=37892, stack(0x000000753f300000,0x000000753f400000) (1024K)]
  0x000002165129cd70 JavaThread "Unconstrained build operations Thread 187"        [_thread_blocked, id=40336, stack(0x000000753f400000,0x000000753f500000) (1024K)]
  0x000002165129e7b0 JavaThread "Unconstrained build operations Thread 188"        [_thread_blocked, id=40236, stack(0x000000753f500000,0x000000753f600000) (1024K)]
  0x00000216579e5740 JavaThread "Cache worker for execution history cache (E:\StudioProjects\AIOSService\.gradle\8.13\executionHistory)"        [_thread_blocked, id=20052, stack(0x000000753b800000,0x000000753b900000) (1024K)]
  0x00000216579e9250 Thread "Unknown thread"                        [id=42924, stack(0x000000753ba00000,0x000000753bb00000) (1024K)]
  0x00000216579ea600 JavaThread "Unconstrained build operations Thread 189"        [_thread_blocked, id=25472, stack(0x000000753f800000,0x000000753f900000) (1024K)]
  0x0000021652923640 JavaThread "Unconstrained build operations Thread 190"        [_thread_blocked, id=9180, stack(0x000000752dd00000,0x000000752de00000) (1024K)]
  0x0000021652922920 JavaThread "Unconstrained build operations Thread 191"        [_thread_blocked, id=5280, stack(0x000000752eb00000,0x000000752ec00000) (1024K)]
  0x00000216579e6460 JavaThread "Unconstrained build operations Thread 192"        [_thread_blocked, id=1396, stack(0x0000007540700000,0x0000007540800000) (1024K)]
  0x00000216579e6af0 JavaThread "Unconstrained build operations Thread 193"        [_thread_blocked, id=43368, stack(0x0000007540800000,0x0000007540900000) (1024K)]
  0x00000216579d2960 JavaThread "Unconstrained build operations Thread 194"        [_thread_blocked, id=31940, stack(0x0000007540900000,0x0000007540a00000) (1024K)]
  0x00000216579d1c40 JavaThread "Unconstrained build operations Thread 195"        [_thread_blocked, id=43468, stack(0x0000007540a00000,0x0000007540b00000) (1024K)]
  0x00000216579d0200 JavaThread "Unconstrained build operations Thread 196"        [_thread_blocked, id=17220, stack(0x0000007540b00000,0x0000007540c00000) (1024K)]
  0x00000216579d3d10 JavaThread "Unconstrained build operations Thread 197"        [_thread_blocked, id=16136, stack(0x0000007540c00000,0x0000007540d00000) (1024K)]
  0x00000216579d7820 JavaThread "Unconstrained build operations Thread 198"        [_thread_blocked, id=20332, stack(0x0000007540d00000,0x0000007540e00000) (1024K)]
  0x00000216579d4a30 JavaThread "Unconstrained build operations Thread 199"        [_thread_blocked, id=27940, stack(0x0000007540e00000,0x0000007540f00000) (1024K)]
  0x00000216579d43a0 JavaThread "Unconstrained build operations Thread 200"        [_thread_blocked, id=42632, stack(0x0000007540f00000,0x0000007541000000) (1024K)]
  0x0000021658c3cca0 JavaThread "C1 CompilerThread1"         daemon [_thread_blocked, id=36208, stack(0x000000752c500000,0x000000752c600000) (1024K)]
  0x0000021658c3e110 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=36328, stack(0x000000752c600000,0x000000752c700000) (1024K)]
  0x0000021658c3e7e0 JavaThread "C1 CompilerThread2"         daemon [_thread_blocked, id=7404, stack(0x000000752c700000,0x000000752c800000) (1024K)]
  0x0000021658c3bf00 JavaThread "C1 CompilerThread3"         daemon [_thread_blocked, id=21712, stack(0x000000752d900000,0x000000752da00000) (1024K)]
=>0x0000021658c3da40 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=41240, stack(0x000000752da00000,0x000000752db00000) (1024K)]
  0x0000021658c3d370 JavaThread "C2 CompilerThread3"         daemon [_thread_in_native, id=38280, stack(0x000000752dc00000,0x000000752dd00000) (1024K)]
  0x0000021658c3b830 JavaThread "C2 CompilerThread4"         daemon [_thread_in_native, id=6960, stack(0x0000007537900000,0x0000007537a00000) (1024K)]
  0x0000021658c41790 JavaThread "C2 CompilerThread5"         daemon [_thread_in_native, id=5728, stack(0x000000753bb00000,0x000000753bc00000) (1024K)]
Total: 289

Other Threads:
  0x000002164638fca0 VMThread "VM Thread"                           [id=44124, stack(0x000000752cf00000,0x000000752d000000) (1024K)]
  0x00000216460c23e0 WatcherThread "VM Periodic Task Thread"        [id=38932, stack(0x000000752ce00000,0x000000752cf00000) (1024K)]
  0x000002162b7de060 WorkerThread "GC Thread#0"                     [id=43288, stack(0x000000752c900000,0x000000752ca00000) (1024K)]
  0x000002164b0764b0 WorkerThread "GC Thread#1"                     [id=17848, stack(0x000000752e000000,0x000000752e100000) (1024K)]
  0x000002164b076850 WorkerThread "GC Thread#2"                     [id=36084, stack(0x000000752e100000,0x000000752e200000) (1024K)]
  0x000002164b22d530 WorkerThread "GC Thread#3"                     [id=40756, stack(0x000000752e200000,0x000000752e300000) (1024K)]
  0x000002164b22d8d0 WorkerThread "GC Thread#4"                     [id=42980, stack(0x000000752e300000,0x000000752e400000) (1024K)]
  0x0000021646ce8be0 WorkerThread "GC Thread#5"                     [id=29712, stack(0x000000752e400000,0x000000752e500000) (1024K)]
  0x0000021646ce9390 WorkerThread "GC Thread#6"                     [id=22176, stack(0x000000752e500000,0x000000752e600000) (1024K)]
  0x0000021646ce9730 WorkerThread "GC Thread#7"                     [id=44760, stack(0x000000752e600000,0x000000752e700000) (1024K)]
  0x0000021646ce9ad0 WorkerThread "GC Thread#8"                     [id=31248, stack(0x000000752e700000,0x000000752e800000) (1024K)]
  0x0000021646bd6210 WorkerThread "GC Thread#9"                     [id=38292, stack(0x000000752e800000,0x000000752e900000) (1024K)]
  0x000002164b0fa420 WorkerThread "GC Thread#10"                    [id=18364, stack(0x000000752e900000,0x000000752ea00000) (1024K)]
  0x000002164b151190 WorkerThread "GC Thread#11"                    [id=14432, stack(0x000000752ea00000,0x000000752eb00000) (1024K)]
  0x000002164b150310 WorkerThread "GC Thread#12"                    [id=3328, stack(0x000000752f300000,0x000000752f400000) (1024K)]
  0x000002164b1506b0 WorkerThread "GC Thread#13"                    [id=17232, stack(0x000000752f400000,0x000000752f500000) (1024K)]
  0x000002164b150a50 WorkerThread "GC Thread#14"                    [id=21180, stack(0x000000752f500000,0x000000752f600000) (1024K)]
  0x000002162be4a160 ConcurrentGCThread "G1 Main Marker"            [id=20884, stack(0x000000752ca00000,0x000000752cb00000) (1024K)]
  0x000002162be4ac60 WorkerThread "G1 Conc#0"                       [id=23212, stack(0x000000752cb00000,0x000000752cc00000) (1024K)]
  0x000002164b150df0 WorkerThread "G1 Conc#1"                       [id=36220, stack(0x000000752f600000,0x000000752f700000) (1024K)]
  0x000002164c861d90 WorkerThread "G1 Conc#2"                       [id=31196, stack(0x000000752f700000,0x000000752f800000) (1024K)]
  0x000002164c862130 WorkerThread "G1 Conc#3"                       [id=24444, stack(0x000000752f800000,0x000000752f900000) (1024K)]
  0x0000021646232410 ConcurrentGCThread "G1 Refine#0"               [id=20148, stack(0x000000752cc00000,0x000000752cd00000) (1024K)]
  0x0000021646232d30 ConcurrentGCThread "G1 Service"                [id=23720, stack(0x000000752cd00000,0x000000752ce00000) (1024K)]
Total: 24

Threads with active compile tasks:
C2 CompilerThread0  154741 20937       4       org.gradle.api.internal.artifacts.ModuleComponentSelectorSerializer::read (50 bytes)
C2 CompilerThread1  154741 20725       4       org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$SupportedPropertyInvoker::invoke (154 bytes)
C2 CompilerThread2  154741 20759       4       org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$InvocationHandlerImpl::invoke (176 bytes)
C2 CompilerThread3  154741 20726       4       org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$SafeMethodInvoker::invoke (118 bytes)
C2 CompilerThread4  154741 20727       4       org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$PropertyCachingMethodInvoker::invoke (110 bytes)
C2 CompilerThread5  154741 20949   !   4       org.gradle.cache.internal.btree.BTreePersistentIndexedCache::get (57 bytes)
Total: 6

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000100000000-0x0000000140000000, reserved size: 1073741824
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3, Narrow klass range: 0x140000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 20 total, 20 available
 Memory: 32492M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 508M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 15
 Concurrent Workers: 4
 Concurrent Refinement Workers: 15
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 378880K, used 279676K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 57 young (58368K), 12 survivors (12288K)
 Metaspace       used 161189K, committed 163904K, reserved 1245184K
  class space    used 21578K, committed 22912K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%|HS|  |TAMS 0x0000000080000000| PB 0x0000000080000000| Complete 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HC|  |TAMS 0x0000000080100000| PB 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080200000| PB 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%| O|  |TAMS 0x0000000080300000| PB 0x0000000080300000| Untracked 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080400000| PB 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080500000| PB 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%|HS|  |TAMS 0x0000000080600000| PB 0x0000000080600000| Complete 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%| O|  |TAMS 0x0000000080700000| PB 0x0000000080700000| Untracked 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080800000| PB 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080900000| PB 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080a00000| PB 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080b00000| PB 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080c00000| PB 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080d00000| PB 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080e00000| PB 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000080f00000| PB 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081000000| PB 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081100000| PB 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081200000| PB 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081300000| PB 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081400000| PB 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081500000| PB 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%|HS|  |TAMS 0x0000000081600000| PB 0x0000000081600000| Complete 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%|HC|  |TAMS 0x0000000081700000| PB 0x0000000081700000| Complete 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%|HC|  |TAMS 0x0000000081800000| PB 0x0000000081800000| Complete 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%|HC|  |TAMS 0x0000000081900000| PB 0x0000000081900000| Complete 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%|HC|  |TAMS 0x0000000081a00000| PB 0x0000000081a00000| Complete 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%|HC|  |TAMS 0x0000000081b00000| PB 0x0000000081b00000| Complete 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%|HS|  |TAMS 0x0000000081c00000| PB 0x0000000081c00000| Complete 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%|HS|  |TAMS 0x0000000081d00000| PB 0x0000000081d00000| Complete 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%|HC|  |TAMS 0x0000000081e00000| PB 0x0000000081e00000| Complete 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%|HC|  |TAMS 0x0000000081f00000| PB 0x0000000081f00000| Complete 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%|HC|  |TAMS 0x0000000082000000| PB 0x0000000082000000| Complete 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%|HS|  |TAMS 0x0000000082100000| PB 0x0000000082100000| Complete 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%|HC|  |TAMS 0x0000000082200000| PB 0x0000000082200000| Complete 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%|HS|  |TAMS 0x0000000082300000| PB 0x0000000082300000| Complete 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%|HS|  |TAMS 0x0000000082400000| PB 0x0000000082400000| Complete 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%|HC|  |TAMS 0x0000000082500000| PB 0x0000000082500000| Complete 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%|HC|  |TAMS 0x0000000082600000| PB 0x0000000082600000| Complete 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%|HC|  |TAMS 0x0000000082700000| PB 0x0000000082700000| Complete 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%|HC|  |TAMS 0x0000000082800000| PB 0x0000000082800000| Complete 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%|HC|  |TAMS 0x0000000082900000| PB 0x0000000082900000| Complete 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%|HS|  |TAMS 0x0000000082a00000| PB 0x0000000082a00000| Complete 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%|HS|  |TAMS 0x0000000082b00000| PB 0x0000000082b00000| Complete 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%|HC|  |TAMS 0x0000000082c00000| PB 0x0000000082c00000| Complete 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%|HC|  |TAMS 0x0000000082d00000| PB 0x0000000082d00000| Complete 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%|HC|  |TAMS 0x0000000082e00000| PB 0x0000000082e00000| Complete 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%|HS|  |TAMS 0x0000000082f00000| PB 0x0000000082f00000| Complete 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%|HC|  |TAMS 0x0000000083000000| PB 0x0000000083000000| Complete 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%|HS|  |TAMS 0x0000000083100000| PB 0x0000000083100000| Complete 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%| O|  |TAMS 0x0000000083200000| PB 0x0000000083200000| Untracked 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%| O|  |TAMS 0x0000000083300000| PB 0x0000000083300000| Untracked 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%| O|  |TAMS 0x0000000083400000| PB 0x0000000083400000| Untracked 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%| O|  |TAMS 0x0000000083500000| PB 0x0000000083500000| Untracked 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%| O|  |TAMS 0x0000000083600000| PB 0x0000000083600000| Untracked 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%| O|  |TAMS 0x0000000083700000| PB 0x0000000083700000| Untracked 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%| O|  |TAMS 0x0000000083800000| PB 0x0000000083800000| Untracked 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%| O|  |TAMS 0x0000000083900000| PB 0x0000000083900000| Untracked 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%| O|  |TAMS 0x0000000083a00000| PB 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%|HS|  |TAMS 0x0000000083b00000| PB 0x0000000083b00000| Complete 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%| O|  |TAMS 0x0000000083c00000| PB 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%| O|  |TAMS 0x0000000083d00000| PB 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%| O|  |TAMS 0x0000000083e00000| PB 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%| O|  |TAMS 0x0000000083f00000| PB 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%|HS|  |TAMS 0x0000000084000000| PB 0x0000000084000000| Complete 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%|HC|  |TAMS 0x0000000084100000| PB 0x0000000084100000| Complete 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%|HS|  |TAMS 0x0000000084200000| PB 0x0000000084200000| Complete 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%|HC|  |TAMS 0x0000000084300000| PB 0x0000000084300000| Complete 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%|HC|  |TAMS 0x0000000084400000| PB 0x0000000084400000| Complete 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%|HC|  |TAMS 0x0000000084500000| PB 0x0000000084500000| Complete 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%|HC|  |TAMS 0x0000000084600000| PB 0x0000000084600000| Complete 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%|HC|  |TAMS 0x0000000084700000| PB 0x0000000084700000| Complete 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%|HS|  |TAMS 0x0000000084800000| PB 0x0000000084800000| Complete 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%|HC|  |TAMS 0x0000000084900000| PB 0x0000000084900000| Complete 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%|HC|  |TAMS 0x0000000084a00000| PB 0x0000000084a00000| Complete 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%|HC|  |TAMS 0x0000000084b00000| PB 0x0000000084b00000| Complete 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%|HC|  |TAMS 0x0000000084c00000| PB 0x0000000084c00000| Complete 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%|HC|  |TAMS 0x0000000084d00000| PB 0x0000000084d00000| Complete 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%|HS|  |TAMS 0x0000000084e00000| PB 0x0000000084e00000| Complete 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|Cm|TAMS 0x0000000084f00000| PB 0x0000000084f00000| Complete 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085000000| PB 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|Cm|TAMS 0x0000000085100000| PB 0x0000000085100000| Complete 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085200000| PB 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085300000| PB 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085400000| PB 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085500000| PB 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085600000| PB 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085700000| PB 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085800000| PB 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085900000| PB 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085a00000| PB 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%|HS|  |TAMS 0x0000000085b00000| PB 0x0000000085b00000| Complete 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%|HC|  |TAMS 0x0000000085c00000| PB 0x0000000085c00000| Complete 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%|HS|  |TAMS 0x0000000085d00000| PB 0x0000000085d00000| Complete 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%|HC|  |TAMS 0x0000000085e00000| PB 0x0000000085e00000| Complete 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%|HS|  |TAMS 0x0000000085f00000| PB 0x0000000085f00000| Complete 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%|HC|  |TAMS 0x0000000086000000| PB 0x0000000086000000| Complete 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%|HC|  |TAMS 0x0000000086100000| PB 0x0000000086100000| Complete 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%|HS|  |TAMS 0x0000000086200000| PB 0x0000000086200000| Complete 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%|HC|  |TAMS 0x0000000086300000| PB 0x0000000086300000| Complete 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%|HC|  |TAMS 0x0000000086400000| PB 0x0000000086400000| Complete 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086500000| PB 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086600000| PB 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086700000| PB 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086800000| PB 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|  |TAMS 0x0000000086900000| PB 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086a00000| PB 0x0000000086a00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086b00000| PB 0x0000000086b00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%| O|  |TAMS 0x0000000086c00000| PB 0x0000000086c00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086d00000| PB 0x0000000086d00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| O|  |TAMS 0x0000000086e00000| PB 0x0000000086e00000| Untracked 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000086f00000| PB 0x0000000086f00000| Untracked 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087000000| PB 0x0000000087000000| Untracked 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087100000| PB 0x0000000087100000| Untracked 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%| O|  |TAMS 0x0000000087200000| PB 0x0000000087200000| Untracked 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%| O|  |TAMS 0x0000000087300000| PB 0x0000000087300000| Untracked 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%| O|  |TAMS 0x0000000087400000| PB 0x0000000087400000| Untracked 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| O|  |TAMS 0x0000000087500000| PB 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087600000| PB 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087700000| PB 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%| O|  |TAMS 0x0000000087800000| PB 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| O|  |TAMS 0x0000000087900000| PB 0x0000000087900000| Untracked 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%| O|  |TAMS 0x0000000087a00000| PB 0x0000000087a00000| Untracked 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| O|  |TAMS 0x0000000087b00000| PB 0x0000000087b00000| Untracked 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%| O|  |TAMS 0x0000000087c00000| PB 0x0000000087c00000| Untracked 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%| O|  |TAMS 0x0000000087d00000| PB 0x0000000087d00000| Untracked 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%| O|  |TAMS 0x0000000087e00000| PB 0x0000000087e00000| Untracked 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%|HS|  |TAMS 0x0000000087f00000| PB 0x0000000087f00000| Complete 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%|HS|  |TAMS 0x0000000088000000| PB 0x0000000088000000| Complete 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%|HC|  |TAMS 0x0000000088100000| PB 0x0000000088100000| Complete 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%|HS|  |TAMS 0x0000000088200000| PB 0x0000000088200000| Complete 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%|HS|  |TAMS 0x0000000088300000| PB 0x0000000088300000| Complete 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%|HS|  |TAMS 0x0000000088400000| PB 0x0000000088400000| Complete 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%|HC|  |TAMS 0x0000000088500000| PB 0x0000000088500000| Complete 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%|HC|  |TAMS 0x0000000088600000| PB 0x0000000088600000| Complete 
| 135|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%|HC|  |TAMS 0x0000000088700000| PB 0x0000000088700000| Complete 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%|HS|  |TAMS 0x0000000088800000| PB 0x0000000088800000| Complete 
| 137|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%| O|  |TAMS 0x0000000088900000| PB 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| O|  |TAMS 0x0000000088a00000| PB 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| O|  |TAMS 0x0000000088b00000| PB 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%| O|  |TAMS 0x0000000088c00000| PB 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%|HS|  |TAMS 0x0000000088d00000| PB 0x0000000088d00000| Complete 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%|HC|  |TAMS 0x0000000088e00000| PB 0x0000000088e00000| Complete 
| 143|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%|HS|  |TAMS 0x0000000088f00000| PB 0x0000000088f00000| Complete 
| 144|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%| O|  |TAMS 0x0000000089000000| PB 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%| O|  |TAMS 0x0000000089100000| PB 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%| O|  |TAMS 0x0000000089200000| PB 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%| O|  |TAMS 0x0000000089300000| PB 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%| O|  |TAMS 0x0000000089400000| PB 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%| O|  |TAMS 0x0000000089500000| PB 0x0000000089500000| Untracked 
| 150|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%| O|  |TAMS 0x0000000089600000| PB 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089800000, 0x0000000089800000|100%| O|  |TAMS 0x0000000089700000| PB 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%| O|  |TAMS 0x0000000089800000| PB 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089a00000, 0x0000000089a00000|100%| O|  |TAMS 0x0000000089900000| PB 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089b00000, 0x0000000089b00000|100%| O|  |TAMS 0x0000000089a00000| PB 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%| O|  |TAMS 0x0000000089b00000| PB 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%| O|  |TAMS 0x0000000089c00000| PB 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%| O|  |TAMS 0x0000000089d00000| PB 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%| O|  |TAMS 0x0000000089e00000| PB 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x000000008a000000, 0x000000008a000000|100%| O|  |TAMS 0x0000000089f00000| PB 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a100000, 0x000000008a100000|100%| O|Cm|TAMS 0x000000008a000000| PB 0x000000008a000000| Complete 
| 161|0x000000008a100000, 0x000000008a200000, 0x000000008a200000|100%| O|  |TAMS 0x000000008a100000| PB 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a300000, 0x000000008a300000|100%|HS|  |TAMS 0x000000008a200000| PB 0x000000008a200000| Complete 
| 163|0x000000008a300000, 0x000000008a400000, 0x000000008a400000|100%|HC|  |TAMS 0x000000008a300000| PB 0x000000008a300000| Complete 
| 164|0x000000008a400000, 0x000000008a500000, 0x000000008a500000|100%|HC|  |TAMS 0x000000008a400000| PB 0x000000008a400000| Complete 
| 165|0x000000008a500000, 0x000000008a600000, 0x000000008a600000|100%|HC|  |TAMS 0x000000008a500000| PB 0x000000008a500000| Complete 
| 166|0x000000008a600000, 0x000000008a700000, 0x000000008a700000|100%| O|  |TAMS 0x000000008a600000| PB 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a800000, 0x000000008a800000|100%| O|  |TAMS 0x000000008a700000| PB 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%| O|  |TAMS 0x000000008a800000| PB 0x000000008a800000| Untracked 
| 169|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%| O|  |TAMS 0x000000008a900000| PB 0x000000008a900000| Untracked 
| 170|0x000000008aa00000, 0x000000008ab00000, 0x000000008ab00000|100%| O|  |TAMS 0x000000008aa00000| PB 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%| O|  |TAMS 0x000000008ab00000| PB 0x000000008ab00000| Untracked 
| 172|0x000000008ac00000, 0x000000008ad00000, 0x000000008ad00000|100%| O|  |TAMS 0x000000008ac00000| PB 0x000000008ac00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ae00000, 0x000000008ae00000|100%| O|  |TAMS 0x000000008ad00000| PB 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008af00000, 0x000000008af00000|100%| O|  |TAMS 0x000000008ae00000| PB 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008b000000, 0x000000008b000000|100%| O|  |TAMS 0x000000008af00000| PB 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b100000, 0x000000008b100000|100%| O|  |TAMS 0x000000008b000000| PB 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b200000, 0x000000008b200000|100%| O|  |TAMS 0x000000008b100000| PB 0x000000008b100000| Untracked 
| 178|0x000000008b200000, 0x000000008b300000, 0x000000008b300000|100%| O|  |TAMS 0x000000008b200000| PB 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b400000, 0x000000008b400000|100%|HS|  |TAMS 0x000000008b300000| PB 0x000000008b300000| Complete 
| 180|0x000000008b400000, 0x000000008b500000, 0x000000008b500000|100%|HC|  |TAMS 0x000000008b400000| PB 0x000000008b400000| Complete 
| 181|0x000000008b500000, 0x000000008b600000, 0x000000008b600000|100%| O|  |TAMS 0x000000008b500000| PB 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b700000, 0x000000008b700000|100%| O|  |TAMS 0x000000008b600000| PB 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b800000, 0x000000008b800000|100%| O|  |TAMS 0x000000008b700000| PB 0x000000008b700000| Untracked 
| 184|0x000000008b800000, 0x000000008b900000, 0x000000008b900000|100%| O|  |TAMS 0x000000008b800000| PB 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008ba00000, 0x000000008ba00000|100%| O|  |TAMS 0x000000008b900000| PB 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008bb00000, 0x000000008bb00000|100%| O|  |TAMS 0x000000008ba00000| PB 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bc00000, 0x000000008bc00000|100%| O|  |TAMS 0x000000008bb00000| PB 0x000000008bb00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bd00000, 0x000000008bd00000|100%| O|  |TAMS 0x000000008bc00000| PB 0x000000008bc00000| Untracked 
| 189|0x000000008bd00000, 0x000000008be00000, 0x000000008be00000|100%| O|  |TAMS 0x000000008bd00000| PB 0x000000008bd00000| Untracked 
| 190|0x000000008be00000, 0x000000008bf00000, 0x000000008bf00000|100%| O|  |TAMS 0x000000008be00000| PB 0x000000008be00000| Untracked 
| 191|0x000000008bf00000, 0x000000008c000000, 0x000000008c000000|100%| O|  |TAMS 0x000000008bf00000| PB 0x000000008bf00000| Untracked 
| 192|0x000000008c000000, 0x000000008c100000, 0x000000008c100000|100%| O|  |TAMS 0x000000008c000000| PB 0x000000008c000000| Untracked 
| 193|0x000000008c100000, 0x000000008c200000, 0x000000008c200000|100%| O|  |TAMS 0x000000008c100000| PB 0x000000008c100000| Untracked 
| 194|0x000000008c200000, 0x000000008c300000, 0x000000008c300000|100%| O|  |TAMS 0x000000008c200000| PB 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c400000, 0x000000008c400000|100%| O|  |TAMS 0x000000008c300000| PB 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c500000, 0x000000008c500000|100%| O|Cm|TAMS 0x000000008c400000| PB 0x000000008c400000| Complete 
| 197|0x000000008c500000, 0x000000008c600000, 0x000000008c600000|100%| O|  |TAMS 0x000000008c500000| PB 0x000000008c500000| Untracked 
| 198|0x000000008c600000, 0x000000008c700000, 0x000000008c700000|100%| O|  |TAMS 0x000000008c600000| PB 0x000000008c600000| Untracked 
| 199|0x000000008c700000, 0x000000008c800000, 0x000000008c800000|100%| O|  |TAMS 0x000000008c700000| PB 0x000000008c700000| Untracked 
| 200|0x000000008c800000, 0x000000008c900000, 0x000000008c900000|100%| O|  |TAMS 0x000000008c800000| PB 0x000000008c800000| Untracked 
| 201|0x000000008c900000, 0x000000008ca00000, 0x000000008ca00000|100%| O|  |TAMS 0x000000008c900000| PB 0x000000008c900000| Untracked 
| 202|0x000000008ca00000, 0x000000008cb00000, 0x000000008cb00000|100%| O|  |TAMS 0x000000008ca00000| PB 0x000000008ca00000| Untracked 
| 203|0x000000008cb00000, 0x000000008cc00000, 0x000000008cc00000|100%| O|  |TAMS 0x000000008cb00000| PB 0x000000008cb00000| Untracked 
| 204|0x000000008cc00000, 0x000000008cd00000, 0x000000008cd00000|100%| O|Cm|TAMS 0x000000008cc00000| PB 0x000000008cc00000| Complete 
| 205|0x000000008cd00000, 0x000000008ce00000, 0x000000008ce00000|100%| O|  |TAMS 0x000000008cd00000| PB 0x000000008cd00000| Untracked 
| 206|0x000000008ce00000, 0x000000008cf00000, 0x000000008cf00000|100%| O|  |TAMS 0x000000008ce00000| PB 0x000000008ce00000| Untracked 
| 207|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%| O|  |TAMS 0x000000008cf00000| PB 0x000000008cf00000| Untracked 
| 208|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%| O|  |TAMS 0x000000008d000000| PB 0x000000008d000000| Untracked 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%| O|  |TAMS 0x000000008d100000| PB 0x000000008d100000| Untracked 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%| O|  |TAMS 0x000000008d200000| PB 0x000000008d200000| Untracked 
| 211|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%| O|  |TAMS 0x000000008d300000| PB 0x000000008d300000| Untracked 
| 212|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%| O|  |TAMS 0x000000008d400000| PB 0x000000008d400000| Untracked 
| 213|0x000000008d500000, 0x000000008d600000, 0x000000008d600000|100%| O|  |TAMS 0x000000008d500000| PB 0x000000008d500000| Untracked 
| 214|0x000000008d600000, 0x000000008d600000, 0x000000008d700000|  0%| F|  |TAMS 0x000000008d600000| PB 0x000000008d600000| Untracked 
| 215|0x000000008d700000, 0x000000008d700000, 0x000000008d800000|  0%| F|  |TAMS 0x000000008d700000| PB 0x000000008d700000| Untracked 
| 216|0x000000008d800000, 0x000000008d800000, 0x000000008d900000|  0%| F|  |TAMS 0x000000008d800000| PB 0x000000008d800000| Untracked 
| 217|0x000000008d900000, 0x000000008d900000, 0x000000008da00000|  0%| F|  |TAMS 0x000000008d900000| PB 0x000000008d900000| Untracked 
| 218|0x000000008da00000, 0x000000008da00000, 0x000000008db00000|  0%| F|  |TAMS 0x000000008da00000| PB 0x000000008da00000| Untracked 
| 219|0x000000008db00000, 0x000000008db00000, 0x000000008dc00000|  0%| F|  |TAMS 0x000000008db00000| PB 0x000000008db00000| Untracked 
| 220|0x000000008dc00000, 0x000000008dc00000, 0x000000008dd00000|  0%| F|  |TAMS 0x000000008dc00000| PB 0x000000008dc00000| Untracked 
| 221|0x000000008dd00000, 0x000000008dd00000, 0x000000008de00000|  0%| F|  |TAMS 0x000000008dd00000| PB 0x000000008dd00000| Untracked 
| 222|0x000000008de00000, 0x000000008de00000, 0x000000008df00000|  0%| F|  |TAMS 0x000000008de00000| PB 0x000000008de00000| Untracked 
| 223|0x000000008df00000, 0x000000008df00000, 0x000000008e000000|  0%| F|  |TAMS 0x000000008df00000| PB 0x000000008df00000| Untracked 
| 224|0x000000008e000000, 0x000000008e000000, 0x000000008e100000|  0%| F|  |TAMS 0x000000008e000000| PB 0x000000008e000000| Untracked 
| 225|0x000000008e100000, 0x000000008e100000, 0x000000008e200000|  0%| F|  |TAMS 0x000000008e100000| PB 0x000000008e100000| Untracked 
| 226|0x000000008e200000, 0x000000008e200000, 0x000000008e300000|  0%| F|  |TAMS 0x000000008e200000| PB 0x000000008e200000| Untracked 
| 227|0x000000008e300000, 0x000000008e300000, 0x000000008e400000|  0%| F|  |TAMS 0x000000008e300000| PB 0x000000008e300000| Untracked 
| 228|0x000000008e400000, 0x000000008e400000, 0x000000008e500000|  0%| F|  |TAMS 0x000000008e400000| PB 0x000000008e400000| Untracked 
| 229|0x000000008e500000, 0x000000008e500000, 0x000000008e600000|  0%| F|  |TAMS 0x000000008e500000| PB 0x000000008e500000| Untracked 
| 230|0x000000008e600000, 0x000000008e600000, 0x000000008e700000|  0%| F|  |TAMS 0x000000008e600000| PB 0x000000008e600000| Untracked 
| 231|0x000000008e700000, 0x000000008e700000, 0x000000008e800000|  0%| F|  |TAMS 0x000000008e700000| PB 0x000000008e700000| Untracked 
| 232|0x000000008e800000, 0x000000008e800000, 0x000000008e900000|  0%| F|  |TAMS 0x000000008e800000| PB 0x000000008e800000| Untracked 
| 233|0x000000008e900000, 0x000000008e900000, 0x000000008ea00000|  0%| F|  |TAMS 0x000000008e900000| PB 0x000000008e900000| Untracked 
| 234|0x000000008ea00000, 0x000000008ea00000, 0x000000008eb00000|  0%| F|  |TAMS 0x000000008ea00000| PB 0x000000008ea00000| Untracked 
| 235|0x000000008eb00000, 0x000000008eb00000, 0x000000008ec00000|  0%| F|  |TAMS 0x000000008eb00000| PB 0x000000008eb00000| Untracked 
| 236|0x000000008ec00000, 0x000000008ec00000, 0x000000008ed00000|  0%| F|  |TAMS 0x000000008ec00000| PB 0x000000008ec00000| Untracked 
| 237|0x000000008ed00000, 0x000000008ed00000, 0x000000008ee00000|  0%| F|  |TAMS 0x000000008ed00000| PB 0x000000008ed00000| Untracked 
| 238|0x000000008ee00000, 0x000000008ee00000, 0x000000008ef00000|  0%| F|  |TAMS 0x000000008ee00000| PB 0x000000008ee00000| Untracked 
| 239|0x000000008ef00000, 0x000000008ef00000, 0x000000008f000000|  0%| F|  |TAMS 0x000000008ef00000| PB 0x000000008ef00000| Untracked 
| 240|0x000000008f000000, 0x000000008f000000, 0x000000008f100000|  0%| F|  |TAMS 0x000000008f000000| PB 0x000000008f000000| Untracked 
| 241|0x000000008f100000, 0x000000008f100000, 0x000000008f200000|  0%| F|  |TAMS 0x000000008f100000| PB 0x000000008f100000| Untracked 
| 242|0x000000008f200000, 0x000000008f200000, 0x000000008f300000|  0%| F|  |TAMS 0x000000008f200000| PB 0x000000008f200000| Untracked 
| 243|0x000000008f300000, 0x000000008f300000, 0x000000008f400000|  0%| F|  |TAMS 0x000000008f300000| PB 0x000000008f300000| Untracked 
| 244|0x000000008f400000, 0x000000008f400000, 0x000000008f500000|  0%| F|  |TAMS 0x000000008f400000| PB 0x000000008f400000| Untracked 
| 245|0x000000008f500000, 0x000000008f500000, 0x000000008f600000|  0%| F|  |TAMS 0x000000008f500000| PB 0x000000008f500000| Untracked 
| 246|0x000000008f600000, 0x000000008f600000, 0x000000008f700000|  0%| F|  |TAMS 0x000000008f600000| PB 0x000000008f600000| Untracked 
| 247|0x000000008f700000, 0x000000008f700000, 0x000000008f800000|  0%| F|  |TAMS 0x000000008f700000| PB 0x000000008f700000| Untracked 
| 248|0x000000008f800000, 0x000000008f800000, 0x000000008f900000|  0%| F|  |TAMS 0x000000008f800000| PB 0x000000008f800000| Untracked 
| 249|0x000000008f900000, 0x000000008f900000, 0x000000008fa00000|  0%| F|  |TAMS 0x000000008f900000| PB 0x000000008f900000| Untracked 
| 250|0x000000008fa00000, 0x000000008fa00000, 0x000000008fb00000|  0%| F|  |TAMS 0x000000008fa00000| PB 0x000000008fa00000| Untracked 
| 251|0x000000008fb00000, 0x000000008fb00000, 0x000000008fc00000|  0%| F|  |TAMS 0x000000008fb00000| PB 0x000000008fb00000| Untracked 
| 252|0x000000008fc00000, 0x000000008fc00000, 0x000000008fd00000|  0%| F|  |TAMS 0x000000008fc00000| PB 0x000000008fc00000| Untracked 
| 253|0x000000008fd00000, 0x000000008fd00000, 0x000000008fe00000|  0%| F|  |TAMS 0x000000008fd00000| PB 0x000000008fd00000| Untracked 
| 254|0x000000008fe00000, 0x000000008fe00000, 0x000000008ff00000|  0%| F|  |TAMS 0x000000008fe00000| PB 0x000000008fe00000| Untracked 
| 255|0x000000008ff00000, 0x000000008ff00000, 0x0000000090000000|  0%| F|  |TAMS 0x000000008ff00000| PB 0x000000008ff00000| Untracked 
| 256|0x0000000090000000, 0x0000000090000000, 0x0000000090100000|  0%| F|  |TAMS 0x0000000090000000| PB 0x0000000090000000| Untracked 
| 257|0x0000000090100000, 0x0000000090100000, 0x0000000090200000|  0%| F|  |TAMS 0x0000000090100000| PB 0x0000000090100000| Untracked 
| 258|0x0000000090200000, 0x0000000090200000, 0x0000000090300000|  0%| F|  |TAMS 0x0000000090200000| PB 0x0000000090200000| Untracked 
| 259|0x0000000090300000, 0x0000000090300000, 0x0000000090400000|  0%| F|  |TAMS 0x0000000090300000| PB 0x0000000090300000| Untracked 
| 260|0x0000000090400000, 0x000000009041f030, 0x0000000090500000| 12%| S|CS|TAMS 0x0000000090400000| PB 0x0000000090400000| Complete 
| 261|0x0000000090500000, 0x0000000090600000, 0x0000000090600000|100%| S|CS|TAMS 0x0000000090500000| PB 0x0000000090500000| Complete 
| 262|0x0000000090600000, 0x0000000090700000, 0x0000000090700000|100%| S|CS|TAMS 0x0000000090600000| PB 0x0000000090600000| Complete 
| 263|0x0000000090700000, 0x0000000090800000, 0x0000000090800000|100%| S|CS|TAMS 0x0000000090700000| PB 0x0000000090700000| Complete 
| 264|0x0000000090800000, 0x0000000090900000, 0x0000000090900000|100%| S|CS|TAMS 0x0000000090800000| PB 0x0000000090800000| Complete 
| 265|0x0000000090900000, 0x0000000090a00000, 0x0000000090a00000|100%| S|CS|TAMS 0x0000000090900000| PB 0x0000000090900000| Complete 
| 266|0x0000000090a00000, 0x0000000090b00000, 0x0000000090b00000|100%| S|CS|TAMS 0x0000000090a00000| PB 0x0000000090a00000| Complete 
| 267|0x0000000090b00000, 0x0000000090c00000, 0x0000000090c00000|100%| S|CS|TAMS 0x0000000090b00000| PB 0x0000000090b00000| Complete 
| 268|0x0000000090c00000, 0x0000000090d00000, 0x0000000090d00000|100%| S|CS|TAMS 0x0000000090c00000| PB 0x0000000090c00000| Complete 
| 269|0x0000000090d00000, 0x0000000090e00000, 0x0000000090e00000|100%| S|CS|TAMS 0x0000000090d00000| PB 0x0000000090d00000| Complete 
| 270|0x0000000090e00000, 0x0000000090f00000, 0x0000000090f00000|100%| S|CS|TAMS 0x0000000090e00000| PB 0x0000000090e00000| Complete 
| 271|0x0000000090f00000, 0x0000000091000000, 0x0000000091000000|100%| S|CS|TAMS 0x0000000090f00000| PB 0x0000000090f00000| Complete 
| 272|0x0000000091000000, 0x0000000091000000, 0x0000000091100000|  0%| F|  |TAMS 0x0000000091000000| PB 0x0000000091000000| Untracked 
| 273|0x0000000091100000, 0x0000000091100000, 0x0000000091200000|  0%| F|  |TAMS 0x0000000091100000| PB 0x0000000091100000| Untracked 
| 274|0x0000000091200000, 0x0000000091200000, 0x0000000091300000|  0%| F|  |TAMS 0x0000000091200000| PB 0x0000000091200000| Untracked 
| 275|0x0000000091300000, 0x0000000091300000, 0x0000000091400000|  0%| F|  |TAMS 0x0000000091300000| PB 0x0000000091300000| Untracked 
| 276|0x0000000091400000, 0x0000000091400000, 0x0000000091500000|  0%| F|  |TAMS 0x0000000091400000| PB 0x0000000091400000| Untracked 
| 277|0x0000000091500000, 0x0000000091500000, 0x0000000091600000|  0%| F|  |TAMS 0x0000000091500000| PB 0x0000000091500000| Untracked 
| 278|0x0000000091600000, 0x0000000091600000, 0x0000000091700000|  0%| F|  |TAMS 0x0000000091600000| PB 0x0000000091600000| Untracked 
| 279|0x0000000091700000, 0x0000000091700000, 0x0000000091800000|  0%| F|  |TAMS 0x0000000091700000| PB 0x0000000091700000| Untracked 
| 280|0x0000000091800000, 0x0000000091800000, 0x0000000091900000|  0%| F|  |TAMS 0x0000000091800000| PB 0x0000000091800000| Untracked 
| 281|0x0000000091900000, 0x0000000091900000, 0x0000000091a00000|  0%| F|  |TAMS 0x0000000091900000| PB 0x0000000091900000| Untracked 
| 282|0x0000000091a00000, 0x0000000091a00000, 0x0000000091b00000|  0%| F|  |TAMS 0x0000000091a00000| PB 0x0000000091a00000| Untracked 
| 283|0x0000000091b00000, 0x0000000091b00000, 0x0000000091c00000|  0%| F|  |TAMS 0x0000000091b00000| PB 0x0000000091b00000| Untracked 
| 284|0x0000000091c00000, 0x0000000091c00000, 0x0000000091d00000|  0%| F|  |TAMS 0x0000000091c00000| PB 0x0000000091c00000| Untracked 
| 285|0x0000000091d00000, 0x0000000091d00000, 0x0000000091e00000|  0%| F|  |TAMS 0x0000000091d00000| PB 0x0000000091d00000| Untracked 
| 286|0x0000000091e00000, 0x0000000091e00000, 0x0000000091f00000|  0%| F|  |TAMS 0x0000000091e00000| PB 0x0000000091e00000| Untracked 
| 287|0x0000000091f00000, 0x0000000091f00000, 0x0000000092000000|  0%| F|  |TAMS 0x0000000091f00000| PB 0x0000000091f00000| Untracked 
| 288|0x0000000092000000, 0x0000000092000000, 0x0000000092100000|  0%| F|  |TAMS 0x0000000092000000| PB 0x0000000092000000| Untracked 
| 289|0x0000000092100000, 0x0000000092100000, 0x0000000092200000|  0%| F|  |TAMS 0x0000000092100000| PB 0x0000000092100000| Untracked 
| 290|0x0000000092200000, 0x0000000092200000, 0x0000000092300000|  0%| F|  |TAMS 0x0000000092200000| PB 0x0000000092200000| Untracked 
| 291|0x0000000092300000, 0x0000000092300000, 0x0000000092400000|  0%| F|  |TAMS 0x0000000092300000| PB 0x0000000092300000| Untracked 
| 292|0x0000000092400000, 0x0000000092400000, 0x0000000092500000|  0%| F|  |TAMS 0x0000000092400000| PB 0x0000000092400000| Untracked 
| 293|0x0000000092500000, 0x0000000092500000, 0x0000000092600000|  0%| F|  |TAMS 0x0000000092500000| PB 0x0000000092500000| Untracked 
| 294|0x0000000092600000, 0x0000000092600000, 0x0000000092700000|  0%| F|  |TAMS 0x0000000092600000| PB 0x0000000092600000| Untracked 
| 295|0x0000000092700000, 0x0000000092700000, 0x0000000092800000|  0%| F|  |TAMS 0x0000000092700000| PB 0x0000000092700000| Untracked 
| 296|0x0000000092800000, 0x0000000092800000, 0x0000000092900000|  0%| F|  |TAMS 0x0000000092800000| PB 0x0000000092800000| Untracked 
| 297|0x0000000092900000, 0x0000000092900000, 0x0000000092a00000|  0%| F|  |TAMS 0x0000000092900000| PB 0x0000000092900000| Untracked 
| 298|0x0000000092a00000, 0x0000000092a00000, 0x0000000092b00000|  0%| F|  |TAMS 0x0000000092a00000| PB 0x0000000092a00000| Untracked 
| 299|0x0000000092b00000, 0x0000000092b00000, 0x0000000092c00000|  0%| F|  |TAMS 0x0000000092b00000| PB 0x0000000092b00000| Untracked 
| 300|0x0000000092c00000, 0x0000000092c00000, 0x0000000092d00000|  0%| F|  |TAMS 0x0000000092c00000| PB 0x0000000092c00000| Untracked 
| 301|0x0000000092d00000, 0x0000000092d00000, 0x0000000092e00000|  0%| F|  |TAMS 0x0000000092d00000| PB 0x0000000092d00000| Untracked 
| 302|0x0000000092e00000, 0x0000000092e00000, 0x0000000092f00000|  0%| F|  |TAMS 0x0000000092e00000| PB 0x0000000092e00000| Untracked 
| 303|0x0000000092f00000, 0x0000000092f00000, 0x0000000093000000|  0%| F|  |TAMS 0x0000000092f00000| PB 0x0000000092f00000| Untracked 
| 304|0x0000000093000000, 0x0000000093000000, 0x0000000093100000|  0%| F|  |TAMS 0x0000000093000000| PB 0x0000000093000000| Untracked 
| 305|0x0000000093100000, 0x0000000093100000, 0x0000000093200000|  0%| F|  |TAMS 0x0000000093100000| PB 0x0000000093100000| Untracked 
| 306|0x0000000093200000, 0x0000000093200000, 0x0000000093300000|  0%| F|  |TAMS 0x0000000093200000| PB 0x0000000093200000| Untracked 
| 307|0x0000000093300000, 0x0000000093300000, 0x0000000093400000|  0%| F|  |TAMS 0x0000000093300000| PB 0x0000000093300000| Untracked 
| 308|0x0000000093400000, 0x0000000093400000, 0x0000000093500000|  0%| F|  |TAMS 0x0000000093400000| PB 0x0000000093400000| Untracked 
| 309|0x0000000093500000, 0x0000000093500000, 0x0000000093600000|  0%| F|  |TAMS 0x0000000093500000| PB 0x0000000093500000| Untracked 
| 310|0x0000000093600000, 0x0000000093600000, 0x0000000093700000|  0%| F|  |TAMS 0x0000000093600000| PB 0x0000000093600000| Untracked 
| 311|0x0000000093700000, 0x0000000093700000, 0x0000000093800000|  0%| F|  |TAMS 0x0000000093700000| PB 0x0000000093700000| Untracked 
| 312|0x0000000093800000, 0x0000000093800000, 0x0000000093900000|  0%| F|  |TAMS 0x0000000093800000| PB 0x0000000093800000| Untracked 
| 313|0x0000000093900000, 0x0000000093900000, 0x0000000093a00000|  0%| F|  |TAMS 0x0000000093900000| PB 0x0000000093900000| Untracked 
| 314|0x0000000093a00000, 0x0000000093a00000, 0x0000000093b00000|  0%| F|  |TAMS 0x0000000093a00000| PB 0x0000000093a00000| Untracked 
| 315|0x0000000093b00000, 0x0000000093b00000, 0x0000000093c00000|  0%| F|  |TAMS 0x0000000093b00000| PB 0x0000000093b00000| Untracked 
| 316|0x0000000093c00000, 0x0000000093c00000, 0x0000000093d00000|  0%| F|  |TAMS 0x0000000093c00000| PB 0x0000000093c00000| Untracked 
| 317|0x0000000093d00000, 0x0000000093d00000, 0x0000000093e00000|  0%| F|  |TAMS 0x0000000093d00000| PB 0x0000000093d00000| Untracked 
| 318|0x0000000093e00000, 0x0000000093e00000, 0x0000000093f00000|  0%| F|  |TAMS 0x0000000093e00000| PB 0x0000000093e00000| Untracked 
| 319|0x0000000093f00000, 0x0000000093f00000, 0x0000000094000000|  0%| F|  |TAMS 0x0000000093f00000| PB 0x0000000093f00000| Untracked 
| 320|0x0000000094000000, 0x0000000094100000, 0x0000000094100000|100%| E|  |TAMS 0x0000000094000000| PB 0x0000000094000000| Complete 
| 321|0x0000000094100000, 0x0000000094200000, 0x0000000094200000|100%| E|CS|TAMS 0x0000000094100000| PB 0x0000000094100000| Complete 
| 322|0x0000000094200000, 0x0000000094300000, 0x0000000094300000|100%| E|CS|TAMS 0x0000000094200000| PB 0x0000000094200000| Complete 
| 323|0x0000000094300000, 0x0000000094400000, 0x0000000094400000|100%| E|CS|TAMS 0x0000000094300000| PB 0x0000000094300000| Complete 
| 324|0x0000000094400000, 0x0000000094500000, 0x0000000094500000|100%| E|CS|TAMS 0x0000000094400000| PB 0x0000000094400000| Complete 
| 325|0x0000000094500000, 0x0000000094600000, 0x0000000094600000|100%| E|CS|TAMS 0x0000000094500000| PB 0x0000000094500000| Complete 
| 326|0x0000000094600000, 0x0000000094700000, 0x0000000094700000|100%| E|CS|TAMS 0x0000000094600000| PB 0x0000000094600000| Complete 
| 327|0x0000000094700000, 0x0000000094800000, 0x0000000094800000|100%| E|CS|TAMS 0x0000000094700000| PB 0x0000000094700000| Complete 
| 328|0x0000000094800000, 0x0000000094900000, 0x0000000094900000|100%| E|CS|TAMS 0x0000000094800000| PB 0x0000000094800000| Complete 
| 329|0x0000000094900000, 0x0000000094a00000, 0x0000000094a00000|100%| E|CS|TAMS 0x0000000094900000| PB 0x0000000094900000| Complete 
| 330|0x0000000094a00000, 0x0000000094b00000, 0x0000000094b00000|100%| E|CS|TAMS 0x0000000094a00000| PB 0x0000000094a00000| Complete 
| 331|0x0000000094b00000, 0x0000000094c00000, 0x0000000094c00000|100%| E|CS|TAMS 0x0000000094b00000| PB 0x0000000094b00000| Complete 
| 332|0x0000000094c00000, 0x0000000094d00000, 0x0000000094d00000|100%| E|CS|TAMS 0x0000000094c00000| PB 0x0000000094c00000| Complete 
| 333|0x0000000094d00000, 0x0000000094e00000, 0x0000000094e00000|100%| E|CS|TAMS 0x0000000094d00000| PB 0x0000000094d00000| Complete 
| 334|0x0000000094e00000, 0x0000000094f00000, 0x0000000094f00000|100%| E|CS|TAMS 0x0000000094e00000| PB 0x0000000094e00000| Complete 
| 335|0x0000000094f00000, 0x0000000095000000, 0x0000000095000000|100%| E|CS|TAMS 0x0000000094f00000| PB 0x0000000094f00000| Complete 
| 336|0x0000000095000000, 0x0000000095100000, 0x0000000095100000|100%| E|CS|TAMS 0x0000000095000000| PB 0x0000000095000000| Complete 
| 337|0x0000000095100000, 0x0000000095200000, 0x0000000095200000|100%| E|CS|TAMS 0x0000000095100000| PB 0x0000000095100000| Complete 
| 338|0x0000000095200000, 0x0000000095300000, 0x0000000095300000|100%| E|CS|TAMS 0x0000000095200000| PB 0x0000000095200000| Complete 
| 339|0x0000000095300000, 0x0000000095400000, 0x0000000095400000|100%| E|CS|TAMS 0x0000000095300000| PB 0x0000000095300000| Complete 
| 340|0x0000000095400000, 0x0000000095500000, 0x0000000095500000|100%| E|CS|TAMS 0x0000000095400000| PB 0x0000000095400000| Complete 
| 341|0x0000000095500000, 0x0000000095600000, 0x0000000095600000|100%| E|CS|TAMS 0x0000000095500000| PB 0x0000000095500000| Complete 
| 342|0x0000000095600000, 0x0000000095700000, 0x0000000095700000|100%| E|CS|TAMS 0x0000000095600000| PB 0x0000000095600000| Complete 
| 343|0x0000000095700000, 0x0000000095800000, 0x0000000095800000|100%| E|CS|TAMS 0x0000000095700000| PB 0x0000000095700000| Complete 
| 344|0x0000000095800000, 0x0000000095900000, 0x0000000095900000|100%| E|CS|TAMS 0x0000000095800000| PB 0x0000000095800000| Complete 
| 345|0x0000000095900000, 0x00000000959ffc28, 0x0000000095a00000| 99%| E|  |TAMS 0x0000000095900000| PB 0x0000000095900000| Complete 
| 346|0x0000000095a00000, 0x0000000095b00000, 0x0000000095b00000|100%| E|CS|TAMS 0x0000000095a00000| PB 0x0000000095a00000| Complete 
| 347|0x0000000095b00000, 0x0000000095c00000, 0x0000000095c00000|100%| E|CS|TAMS 0x0000000095b00000| PB 0x0000000095b00000| Complete 
| 348|0x0000000095c00000, 0x0000000095d00000, 0x0000000095d00000|100%| E|CS|TAMS 0x0000000095c00000| PB 0x0000000095c00000| Complete 
| 349|0x0000000095d00000, 0x0000000095e00000, 0x0000000095e00000|100%| E|CS|TAMS 0x0000000095d00000| PB 0x0000000095d00000| Complete 
| 350|0x0000000095e00000, 0x0000000095f00000, 0x0000000095f00000|100%| E|CS|TAMS 0x0000000095e00000| PB 0x0000000095e00000| Complete 
| 351|0x0000000095f00000, 0x0000000096000000, 0x0000000096000000|100%| E|CS|TAMS 0x0000000095f00000| PB 0x0000000095f00000| Complete 
| 352|0x0000000096000000, 0x0000000096100000, 0x0000000096100000|100%| E|CS|TAMS 0x0000000096000000| PB 0x0000000096000000| Complete 
| 353|0x0000000096100000, 0x0000000096200000, 0x0000000096200000|100%| E|CS|TAMS 0x0000000096100000| PB 0x0000000096100000| Complete 
| 480|0x000000009e000000, 0x000000009e100000, 0x000000009e100000|100%| E|CS|TAMS 0x000000009e000000| PB 0x000000009e000000| Complete 
| 481|0x000000009e100000, 0x000000009e200000, 0x000000009e200000|100%| E|CS|TAMS 0x000000009e100000| PB 0x000000009e100000| Complete 
| 482|0x000000009e200000, 0x000000009e300000, 0x000000009e300000|100%| E|CS|TAMS 0x000000009e200000| PB 0x000000009e200000| Complete 
| 483|0x000000009e300000, 0x000000009e400000, 0x000000009e400000|100%| E|CS|TAMS 0x000000009e300000| PB 0x000000009e300000| Complete 
| 484|0x000000009e400000, 0x000000009e500000, 0x000000009e500000|100%| E|CS|TAMS 0x000000009e400000| PB 0x000000009e400000| Complete 
| 485|0x000000009e500000, 0x000000009e600000, 0x000000009e600000|100%| E|CS|TAMS 0x000000009e500000| PB 0x000000009e500000| Complete 
| 486|0x000000009e600000, 0x000000009e700000, 0x000000009e700000|100%| E|CS|TAMS 0x000000009e600000| PB 0x000000009e600000| Complete 
| 487|0x000000009e700000, 0x000000009e800000, 0x000000009e800000|100%| E|CS|TAMS 0x000000009e700000| PB 0x000000009e700000| Complete 
| 488|0x000000009e800000, 0x000000009e900000, 0x000000009e900000|100%| E|CS|TAMS 0x000000009e800000| PB 0x000000009e800000| Complete 
| 489|0x000000009e900000, 0x000000009ea00000, 0x000000009ea00000|100%| E|CS|TAMS 0x000000009e900000| PB 0x000000009e900000| Complete 
| 507|0x000000009fb00000, 0x000000009fc00000, 0x000000009fc00000|100%| E|CS|TAMS 0x000000009fb00000| PB 0x000000009fb00000| Complete 
|2043|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| O|  |TAMS 0x00000000ffb00000| PB 0x00000000ffb00000| Untracked 
|2044|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| O|  |TAMS 0x00000000ffc00000| PB 0x00000000ffc00000| Untracked 
|2045|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| O|  |TAMS 0x00000000ffd00000| PB 0x00000000ffd00000| Untracked 
|2046|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| O|  |TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Untracked 
|2047|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| O|  |TAMS 0x00000000fff00000| PB 0x00000000fff00000| Untracked 

Card table byte_map: [0x0000021640af0000,0x0000021640ef0000] _byte_map_base: 0x00000216406f0000

Marking Bits: (CMBitMap*) 0x000002162be38a40
 Bits: [0x0000021640ef0000, 0x0000021642ef0000)

Polling page: 0x000002162bb10000

Metaspace:

Usage:
  Non-class:    136.38 MB used.
      Class:     21.08 MB used.
       Both:    157.46 MB used.

Virtual space:
  Non-class space:      192.00 MB reserved,     137.81 MB ( 72%) committed,  3 nodes.
      Class space:        1.00 GB reserved,      22.38 MB (  2%) committed,  1 nodes.
             Both:        1.19 GB reserved,     160.19 MB ( 13%) committed. 

Chunk freelists:
   Non-Class:  6.12 MB
       Class:  9.44 MB
        Both:  15.56 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 251.69 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 18.
num_arena_births: 5112.
num_arena_deaths: 6.
num_vsnodes_births: 4.
num_vsnodes_deaths: 0.
num_space_committed: 2562.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 35.
num_chunks_taken_from_freelist: 12239.
num_chunk_merges: 17.
num_chunk_splits: 7884.
num_chunks_enlarged: 4894.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=119168Kb used=12663Kb max_used=12663Kb free=106504Kb
 bounds [0x00000216380c0000, 0x0000021638d20000, 0x000002163f520000]
CodeHeap 'profiled nmethods': size=119104Kb used=35299Kb max_used=35299Kb free=83804Kb
 bounds [0x0000021630520000, 0x00000216327a0000, 0x0000021637970000]
CodeHeap 'non-nmethods': size=7488Kb used=4781Kb max_used=4835Kb free=2706Kb
 bounds [0x0000021637970000, 0x0000021637ea0000, 0x00000216380c0000]
 total_blobs=19094 nmethods=17951 adapters=1043
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 154.475 Thread 0x0000021658c3cca0 nmethod 20967 0x000002163276aa90 code [0x000002163276aca0, 0x000002163276b180]
Event: 154.475 Thread 0x0000021646483700 nmethod 20966 0x000002163276b410 code [0x000002163276b620, 0x000002163276bb48]
Event: 154.475 Thread 0x0000021658c3bf00 nmethod 20969 0x000002163276be10 code [0x000002163276c000, 0x000002163276c578]
Event: 154.475 Thread 0x0000021658c3bf00 20971       3       org.gradle.api.internal.artifacts.ivyservice.modulecache.artifacts.InMemoryModuleArtifactsCache::get (52 bytes)
Event: 154.475 Thread 0x0000021658c3cca0 20970       3       org.gradle.api.internal.artifacts.ivyservice.modulecache.artifacts.ArtifactsAtRepositoryKey::<init> (20 bytes)
Event: 154.475 Thread 0x0000021646483700 20972       3       org.gradle.api.internal.artifacts.ivyservice.modulecache.artifacts.AbstractArtifactsCache::createCacheArtifacts (30 bytes)
Event: 154.475 Thread 0x0000021658c3cca0 nmethod 20970 0x000002163276c710 code [0x000002163276c8c0, 0x000002163276cb40]
Event: 154.475 Thread 0x0000021646483700 nmethod 20972 0x000002163276cc10 code [0x000002163276cdc0, 0x000002163276d0e8]
Event: 154.475 Thread 0x0000021646483700 20973       3       org.gradle.api.internal.artifacts.ivyservice.modulecache.artifacts.DefaultCachedArtifacts::<init> (20 bytes)
Event: 154.475 Thread 0x0000021658c3cca0 20974       3       org.gradle.api.internal.artifacts.ivyservice.DefaultCacheExpirationControl::moduleArtifactsExpiry (36 bytes)
Event: 154.475 Thread 0x0000021658c3bf00 nmethod 20971 0x000002163276d290 code [0x000002163276d480, 0x000002163276d980]
Event: 154.475 Thread 0x0000021646483700 nmethod 20973 0x000002163276db10 code [0x000002163276dcc0, 0x000002163276dee8]
Event: 154.475 Thread 0x0000021658c3cca0 nmethod 20974 0x000002163276df90 code [0x000002163276e160, 0x000002163276e5c8]
Event: 154.476 Thread 0x0000021658c3e7e0 nmethod 20968 0x000002163276e810 code [0x000002163276ebe0, 0x00000216327708c0]
Event: 154.483 Thread 0x0000021658c3cca0 20975       3       org.gradle.api.internal.artifacts.repositories.resolver.MavenResolver$MavenLocalRepositoryAccess::resolveSourceArtifacts (1 bytes)
Event: 154.483 Thread 0x0000021658c3cca0 nmethod 20975 0x0000021632771310 code [0x00000216327714a0, 0x00000216327715a8]
Event: 154.497 Thread 0x0000021646483700 20976       3       org.gradle.internal.component.external.model.JavaEcosystemVariantDerivationStrategy::libraryWithSourcesVariant (44 bytes)
Event: 154.497 Thread 0x0000021658c3cca0 20977       3       org.gradle.api.internal.artifacts.repositories.metadata.DefaultMavenAttributesFactory::sourcesVariant (32 bytes)
Event: 154.497 Thread 0x0000021658c3bf00 20978       3       org.gradle.api.internal.artifacts.repositories.metadata.DefaultMavenAttributesFactory$$Lambda/0x0000000100af39e0::<init> (15 bytes)
Event: 154.497 Thread 0x0000021658c3e7e0 20979       3       org.gradle.internal.component.external.model.JavaEcosystemVariantDerivationStrategy::libraryWithJavadocVariant (44 bytes)

GC Heap History (20 events):
Event: 11.944 GC heap before
{Heap before GC invocations=26 (full 0):
 garbage-first heap   total 316416K, used 282900K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 102 young (104448K), 10 survivors (10240K)
 Metaspace       used 128017K, committed 130560K, reserved 1179648K
  class space    used 17552K, committed 18816K, reserved 1048576K
}
Event: 11.948 GC heap after
{Heap after GC invocations=27 (full 0):
 garbage-first heap   total 316416K, used 192285K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 128017K, committed 130560K, reserved 1179648K
  class space    used 17552K, committed 18816K, reserved 1048576K
}
Event: 12.308 GC heap before
{Heap before GC invocations=27 (full 0):
 garbage-first heap   total 316416K, used 289565K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 104 young (106496K), 9 survivors (9216K)
 Metaspace       used 128793K, committed 131328K, reserved 1179648K
  class space    used 17614K, committed 18880K, reserved 1048576K
}
Event: 12.312 GC heap after
{Heap after GC invocations=28 (full 0):
 garbage-first heap   total 316416K, used 195877K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 128793K, committed 131328K, reserved 1179648K
  class space    used 17614K, committed 18880K, reserved 1048576K
}
Event: 12.611 GC heap before
{Heap before GC invocations=28 (full 0):
 garbage-first heap   total 316416K, used 224549K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 39 young (39936K), 10 survivors (10240K)
 Metaspace       used 130515K, committed 133120K, reserved 1179648K
  class space    used 17887K, committed 19136K, reserved 1048576K
}
Event: 12.615 GC heap after
{Heap after GC invocations=29 (full 0):
 garbage-first heap   total 316416K, used 196054K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 130515K, committed 133120K, reserved 1179648K
  class space    used 17887K, committed 19136K, reserved 1048576K
}
Event: 13.480 GC heap before
{Heap before GC invocations=30 (full 0):
 garbage-first heap   total 331776K, used 287190K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 94 young (96256K), 6 survivors (6144K)
 Metaspace       used 135480K, committed 138112K, reserved 1179648K
  class space    used 18520K, committed 19840K, reserved 1048576K
}
Event: 13.485 GC heap after
{Heap after GC invocations=31 (full 0):
 garbage-first heap   total 331776K, used 205345K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 135480K, committed 138112K, reserved 1179648K
  class space    used 18520K, committed 19840K, reserved 1048576K
}
Event: 13.825 GC heap before
{Heap before GC invocations=31 (full 0):
 garbage-first heap   total 331776K, used 299553K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 104 young (106496K), 12 survivors (12288K)
 Metaspace       used 135482K, committed 138112K, reserved 1179648K
  class space    used 18520K, committed 19840K, reserved 1048576K
}
Event: 13.832 GC heap after
{Heap after GC invocations=32 (full 0):
 garbage-first heap   total 331776K, used 207247K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 7 survivors (7168K)
 Metaspace       used 135482K, committed 138112K, reserved 1179648K
  class space    used 18520K, committed 19840K, reserved 1048576K
}
Event: 14.168 GC heap before
{Heap before GC invocations=32 (full 0):
 garbage-first heap   total 331776K, used 301455K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 99 young (101376K), 7 survivors (7168K)
 Metaspace       used 135482K, committed 138112K, reserved 1179648K
  class space    used 18520K, committed 19840K, reserved 1048576K
}
Event: 14.172 GC heap after
{Heap after GC invocations=33 (full 0):
 garbage-first heap   total 331776K, used 210173K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 135482K, committed 138112K, reserved 1179648K
  class space    used 18520K, committed 19840K, reserved 1048576K
}
Event: 15.258 GC heap before
{Heap before GC invocations=34 (full 0):
 garbage-first heap   total 352256K, used 304381K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 100 young (102400K), 9 survivors (9216K)
 Metaspace       used 144537K, committed 147200K, reserved 1179648K
  class space    used 19648K, committed 20928K, reserved 1048576K
}
Event: 15.263 GC heap after
{Heap after GC invocations=35 (full 0):
 garbage-first heap   total 352256K, used 212590K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 144537K, committed 147200K, reserved 1179648K
  class space    used 19648K, committed 20928K, reserved 1048576K
}
Event: 19.232 GC heap before
{Heap before GC invocations=35 (full 0):
 garbage-first heap   total 352256K, used 325230K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 115 young (117760K), 9 survivors (9216K)
 Metaspace       used 149420K, committed 152064K, reserved 1245184K
  class space    used 20140K, committed 21440K, reserved 1048576K
}
Event: 19.238 GC heap after
{Heap after GC invocations=36 (full 0):
 garbage-first heap   total 352256K, used 219238K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 14 young (14336K), 14 survivors (14336K)
 Metaspace       used 149420K, committed 152064K, reserved 1245184K
  class space    used 20140K, committed 21440K, reserved 1048576K
}
Event: 19.912 GC heap before
{Heap before GC invocations=36 (full 0):
 garbage-first heap   total 352256K, used 318566K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 111 young (113664K), 14 survivors (14336K)
 Metaspace       used 151645K, committed 154368K, reserved 1245184K
  class space    used 20388K, committed 21696K, reserved 1048576K
}
Event: 19.918 GC heap after
{Heap after GC invocations=37 (full 0):
 garbage-first heap   total 352256K, used 226614K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 151645K, committed 154368K, reserved 1245184K
  class space    used 20388K, committed 21696K, reserved 1048576K
}
Event: 21.462 GC heap before
{Heap before GC invocations=38 (full 0):
 garbage-first heap   total 378880K, used 320822K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 103 young (105472K), 10 survivors (10240K)
 Metaspace       used 158703K, committed 161472K, reserved 1245184K
  class space    used 21304K, committed 22656K, reserved 1048576K
}
Event: 21.468 GC heap after
{Heap after GC invocations=39 (full 0):
 garbage-first heap   total 378880K, used 235644K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 158703K, committed 161472K, reserved 1245184K
  class space    used 21304K, committed 22656K, reserved 1048576K
}

Dll operation events (3 events):
Event: 0.007 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 0.011 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
Event: 0.319 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 154.373 Thread 0x000002164c97ee20 DEOPT PACKING pc=0x0000021638b0d154 sp=0x000000752f1fa000
Event: 154.373 Thread 0x000002164c97ee20 DEOPT UNPACKING pc=0x00000216379c46a2 sp=0x000000752f1f9fb0 mode 2
Event: 154.373 Thread 0x000002164c97ee20 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000021638a131ec relative=0x0000000000000b0c
Event: 154.373 Thread 0x000002164c97ee20 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000021638a131ec method=com.google.common.collect.AbstractMapBasedMultimap.iteratorOrListIterator(Ljava/util/Collection;)Ljava/util/Iterator; @ 20 c2
Event: 154.373 Thread 0x000002164c97ee20 DEOPT PACKING pc=0x0000021638a131ec sp=0x000000752f1fa0b0
Event: 154.373 Thread 0x000002164c97ee20 DEOPT UNPACKING pc=0x00000216379c46a2 sp=0x000000752f1f9f58 mode 2
Event: 154.373 Thread 0x000002164c97ee20 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000021638a131ec relative=0x0000000000000b0c
Event: 154.373 Thread 0x000002164c97ee20 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000021638a131ec method=com.google.common.collect.AbstractMapBasedMultimap.iteratorOrListIterator(Ljava/util/Collection;)Ljava/util/Iterator; @ 20 c2
Event: 154.373 Thread 0x000002164c97ee20 DEOPT PACKING pc=0x0000021638a131ec sp=0x000000752f1fa0b0
Event: 154.373 Thread 0x000002164c97ee20 DEOPT UNPACKING pc=0x00000216379c46a2 sp=0x000000752f1f9f58 mode 2
Event: 154.373 Thread 0x000002164c97ee20 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000021638a131ec relative=0x0000000000000b0c
Event: 154.373 Thread 0x000002164c97ee20 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000021638a131ec method=com.google.common.collect.AbstractMapBasedMultimap.iteratorOrListIterator(Ljava/util/Collection;)Ljava/util/Iterator; @ 20 c2
Event: 154.373 Thread 0x000002164c97ee20 DEOPT PACKING pc=0x0000021638a131ec sp=0x000000752f1fa0b0
Event: 154.373 Thread 0x000002164c97ee20 DEOPT UNPACKING pc=0x00000216379c46a2 sp=0x000000752f1f9f58 mode 2
Event: 154.373 Thread 0x000002164c97ee20 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000021638a131ec relative=0x0000000000000b0c
Event: 154.373 Thread 0x000002164c97ee20 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000021638a131ec method=com.google.common.collect.AbstractMapBasedMultimap.iteratorOrListIterator(Ljava/util/Collection;)Ljava/util/Iterator; @ 20 c2
Event: 154.373 Thread 0x000002164c97ee20 DEOPT PACKING pc=0x0000021638a131ec sp=0x000000752f1fa0b0
Event: 154.373 Thread 0x000002164c97ee20 DEOPT UNPACKING pc=0x00000216379c46a2 sp=0x000000752f1f9f58 mode 2
Event: 154.427 Thread 0x000002164c97ee20 DEOPT PACKING pc=0x00000216325e6299 sp=0x000000752f1f9770
Event: 154.428 Thread 0x000002164c97ee20 DEOPT UNPACKING pc=0x00000216379c4e42 sp=0x000000752f1f8c80 mode 0

Classes loaded (20 events):
Event: 21.220 Loading class com/sun/crypto/provider/Poly1305
Event: 21.220 Loading class com/sun/crypto/provider/Poly1305 done
Event: 21.220 Loading class sun/security/util/math/intpoly/IntegerPolynomial1305
Event: 21.220 Loading class sun/security/util/math/intpoly/IntegerPolynomial1305 done
Event: 21.224 Loading class javax/crypto/NullCipher
Event: 21.224 Loading class javax/crypto/NullCipher done
Event: 21.328 Loading class sun/security/ssl/NewSessionTicket$T12NewSessionTicketMessage
Event: 21.328 Loading class sun/security/ssl/NewSessionTicket$NewSessionTicketMessage
Event: 21.328 Loading class sun/security/ssl/NewSessionTicket$NewSessionTicketMessage done
Event: 21.328 Loading class sun/security/ssl/NewSessionTicket$T12NewSessionTicketMessage done
Event: 21.329 Loading class sun/security/ssl/SSLCipher$T12CC20P1305ReadCipherGenerator$CC20P1305ReadCipher
Event: 21.329 Loading class sun/security/ssl/SSLCipher$T12CC20P1305ReadCipherGenerator$CC20P1305ReadCipher done
Event: 21.329 Loading class com/sun/crypto/provider/ChaCha20Cipher$EngineAEADDec
Event: 21.329 Loading class com/sun/crypto/provider/ChaCha20Cipher$EngineAEADDec done
Event: 21.329 Loading class jdk/internal/event/TLSHandshakeEvent
Event: 21.330 Loading class jdk/internal/event/TLSHandshakeEvent done
Event: 21.330 Loading class javax/net/ssl/SSLEngineResult$HandshakeStatus
Event: 21.330 Loading class javax/net/ssl/SSLEngineResult$HandshakeStatus done
Event: 42.644 Loading class sun/net/util/SocketExceptions
Event: 42.644 Loading class sun/net/util/SocketExceptions done

Classes unloaded (20 events):
Event: 11.208 Thread 0x000002164638fca0 Unloading class 0x0000000100f71800 '_BuildScript_$_run_closure1$_closure4$_closure9'
Event: 11.208 Thread 0x000002164638fca0 Unloading class 0x0000000100f71400 '_BuildScript_$_run_closure1$_closure4'
Event: 11.208 Thread 0x000002164638fca0 Unloading class 0x0000000100f71000 '_BuildScript_$_run_closure1$_closure3$_closure8'
Event: 11.208 Thread 0x000002164638fca0 Unloading class 0x0000000100f70950 '_BuildScript_$_run_closure1$_closure3'
Event: 11.208 Thread 0x000002164638fca0 Unloading class 0x0000000100f70550 '_BuildScript_$_run_closure1'
Event: 11.208 Thread 0x000002164638fca0 Unloading class 0x0000000100f70000 '_BuildScript_'
Event: 14.207 Thread 0x000002164638fca0 Unloading class 0x000000010125e000 '_BuildScript_$_run_closure2'
Event: 14.207 Thread 0x000002164638fca0 Unloading class 0x000000010125dc00 '_BuildScript_$_run_closure1$_closure10$_closure11'
Event: 14.207 Thread 0x000002164638fca0 Unloading class 0x000000010125d800 '_BuildScript_$_run_closure1$_closure10'
Event: 14.207 Thread 0x000002164638fca0 Unloading class 0x000000010125d400 '_BuildScript_$_run_closure1$_closure9'
Event: 14.207 Thread 0x000002164638fca0 Unloading class 0x000000010125d000 '_BuildScript_$_run_closure1$_closure8'
Event: 14.207 Thread 0x000002164638fca0 Unloading class 0x000000010125cc00 '_BuildScript_$_run_closure1$_closure7'
Event: 14.207 Thread 0x000002164638fca0 Unloading class 0x000000010125c800 '_BuildScript_$_run_closure1$_closure6'
Event: 14.207 Thread 0x000002164638fca0 Unloading class 0x000000010125c400 '_BuildScript_$_run_closure1$_closure5'
Event: 14.207 Thread 0x000002164638fca0 Unloading class 0x000000010125c000 '_BuildScript_$_run_closure1$_closure4'
Event: 14.207 Thread 0x000002164638fca0 Unloading class 0x000000010125b938 '_BuildScript_$_run_closure1$_closure3'
Event: 14.207 Thread 0x000002164638fca0 Unloading class 0x000000010125b538 '_BuildScript_$_run_closure1'
Event: 14.207 Thread 0x000002164638fca0 Unloading class 0x000000010125b000 '_BuildScript_'
Event: 14.207 Thread 0x000002164638fca0 Unloading class 0x000000010125a000 '_BuildScript_$_run_closure1'
Event: 14.207 Thread 0x000002164638fca0 Unloading class 0x0000000101259800 '_BuildScript_'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 42.643 Thread 0x000002164c97ee20 Exception <a 'java/net/ConnectException'{0x000000009fb514b0}: Connection timed out: getsockopt> (0x000000009fb514b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 567]
Event: 64.650 Thread 0x000002164c97ee20 Exception <a 'java/net/ConnectException'{0x000000009fb78d58}: Connection timed out: getsockopt> (0x000000009fb78d58) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 567]
Event: 87.657 Thread 0x000002164c97ee20 Exception <a 'java/net/ConnectException'{0x000000009e9df100}: Connection timed out: getsockopt> (0x000000009e9df100) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 567]
Event: 109.176 Thread 0x000002164c97ee20 Exception <a 'java/net/ConnectException'{0x000000009e770358}: Connection timed out: getsockopt> (0x000000009e770358) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 567]
Event: 131.191 Thread 0x000002164c97ee20 Exception <a 'java/net/ConnectException'{0x000000009e60f808}: Connection timed out: getsockopt> (0x000000009e60f808) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 567]
Event: 154.196 Thread 0x000002164c97ee20 Exception <a 'java/net/ConnectException'{0x000000009e627e68}: Connection timed out: getsockopt> (0x000000009e627e68) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 567]
Event: 154.226 Thread 0x00000216579e9250 Exception <a 'java/lang/NoSuchMethodError'{0x000000009e287830}: static Lorg/jetbrains/kotlin/idea/gradleTooling/model/kapt/KaptGradleModelImpl;.<clinit>()V> (0x000000009e287830) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 154.227 Thread 0x00000216579e9250 Exception <a 'java/lang/NoSuchMethodError'{0x000000009e109480}: static Lorg/jetbrains/kotlin/idea/gradleTooling/KotlinTaskPropertiesImpl;.<clinit>()V> (0x000000009e109480) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 154.228 Thread 0x00000216579e9250 Exception <a 'java/lang/NoSuchMethodError'{0x000000009e1368f8}: static Lorg/jetbrains/kotlin/idea/gradleTooling/model/parcelize/ParcelizeGradleModelImpl;.<clinit>()V> (0x000000009e1368f8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 154.232 Thread 0x00000216579e9250 Exception <a 'java/lang/NoSuchMethodError'{0x000000009e14a5a0}: static Lorg/jetbrains/kotlin/idea/gradleTooling/model/assignment/AssignmentModelImpl;.<clinit>()V> (0x000000009e14a5a0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 154.240 Thread 0x00000216579e9250 Exception <a 'java/lang/NoSuchMethodError'{0x000000009e155678}: static Lorg/jetbrains/kotlin/idea/gradleTooling/model/samWithReceiver/SamWithReceiverModelImpl;.<clinit>()V> (0x000000009e155678) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 154.243 Thread 0x00000216579e9250 Exception <a 'java/lang/NoSuchMethodError'{0x000000009e1e3988}: static Lorg/jetbrains/kotlin/idea/gradleTooling/model/noarg/NoArgModelImpl;.<clinit>()V> (0x000000009e1e3988) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 154.245 Thread 0x00000216579e9250 Exception <a 'java/lang/NoSuchMethodError'{0x000000009e1eee38}: static Lorg/jetbrains/kotlin/idea/gradleTooling/model/allopen/AllOpenModelImpl;.<clinit>()V> (0x000000009e1eee38) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 154.266 Thread 0x000002164c97ee20 Exception <a 'java/lang/ClassNotFoundException'{0x0000000096059910}: org/gradle/api/tasks/testing/Test_DecoratedBeanInfo> (0x0000000096059910) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 154.266 Thread 0x000002164c97ee20 Exception <a 'java/lang/ClassNotFoundException'{0x0000000096064e18}: org/gradle/api/tasks/testing/TestBeanInfo> (0x0000000096064e18) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 154.266 Thread 0x000002164c97ee20 Exception <a 'java/lang/ClassNotFoundException'{0x0000000096070570}: org/gradle/api/tasks/testing/AbstractTestTaskBeanInfo> (0x0000000096070570) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 154.266 Thread 0x000002164c97ee20 Exception <a 'java/lang/ClassNotFoundException'{0x000000009607c110}: org/gradle/api/tasks/testing/AbstractTestTaskCustomizer> (0x000000009607c110) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 154.267 Thread 0x000002164c97ee20 Exception <a 'java/lang/ClassNotFoundException'{0x00000000960a4220}: org/gradle/api/tasks/testing/TestCustomizer> (0x00000000960a4220) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 154.271 Thread 0x000002164c97ee20 Exception <a 'java/lang/ClassNotFoundException'{0x0000000095f36578}: org/gradle/api/tasks/testing/Test_DecoratedCustomizer> (0x0000000095f36578) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 154.280 Thread 0x00000216579e9250 Exception <a 'java/lang/NoSuchMethodError'{0x0000000095e96440}: static Lorg/jetbrains/plugins/gradle/tooling/internal/VersionCatalogsModelImpl;.<clinit>()V> (0x0000000095e96440) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 60.171 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation)
Event: 60.171 Executing VM operation: HandshakeAllThreads (HandshakeForDeflation) done
Event: 60.171 Executing VM operation: RendezvousGCThreads
Event: 60.171 Executing VM operation: RendezvousGCThreads done
Event: 62.171 Executing VM operation: Cleanup
Event: 62.171 Executing VM operation: Cleanup done
Event: 73.176 Executing VM operation: Cleanup
Event: 73.176 Executing VM operation: Cleanup done
Event: 88.184 Executing VM operation: Cleanup
Event: 88.184 Executing VM operation: Cleanup done
Event: 89.184 Executing VM operation: Cleanup
Event: 89.184 Executing VM operation: Cleanup done
Event: 110.196 Executing VM operation: Cleanup
Event: 110.196 Executing VM operation: Cleanup done
Event: 131.210 Executing VM operation: Cleanup
Event: 131.210 Executing VM operation: Cleanup done
Event: 154.219 Executing VM operation: Cleanup
Event: 154.219 Executing VM operation: Cleanup done
Event: 154.476 Executing VM operation: ICBufferFull
Event: 154.476 Executing VM operation: ICBufferFull done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x00000216309d9f90
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x0000021630afe710
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x0000021630fa5f90
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x0000021631039590
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x000002163103a210
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x000002163103d890
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x0000021631042090
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x0000021631322c90
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x000002163179c090
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x0000021638386d90
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x000002163866bd90
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x0000021630dfdd10
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x0000021630dfee10
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x0000021630faec90
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x0000021631035210
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x0000021631037790
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x0000021631038490
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x0000021631233710
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x0000021631339d90
Event: 19.980 Thread 0x000002164638fca0 flushing  nmethod 0x0000021631dba310

Events (20 events):
Event: 44.068 Thread 0x0000021652927e70 Thread exited: 0x0000021652927e70
Event: 47.226 Thread 0x00000216579e7ea0 Thread exited: 0x00000216579e7ea0
Event: 50.384 Thread 0x0000021652925da0 Thread exited: 0x0000021652925da0
Event: 72.717 Thread 0x0000021651aac690 Thread exited: 0x0000021651aac690
Event: 73.513 Thread 0x00000216579d0890 Thread exited: 0x00000216579d0890
Event: 78.806 Thread 0x00000216579e8530 Thread exited: 0x00000216579e8530
Event: 78.806 Thread 0x00000216579e8bc0 Thread exited: 0x00000216579e8bc0
Event: 78.811 Thread 0x00000216579e5dd0 Thread exited: 0x00000216579e5dd0
Event: 80.209 Thread 0x00000216579d22d0 Thread exited: 0x00000216579d22d0
Event: 87.666 Thread 0x0000021646483700 Thread added: 0x0000021658c3d370
Event: 87.828 Thread 0x0000021658c3d370 Thread exited: 0x0000021658c3d370
Event: 97.391 Thread 0x000002164d983970 Thread exited: 0x000002164d983970
Event: 154.208 Thread 0x0000021646483700 Thread added: 0x0000021658c3cca0
Event: 154.244 Thread 0x0000021646483700 Thread added: 0x0000021658c3e110
Event: 154.260 Thread 0x0000021646483700 Thread added: 0x0000021658c3e7e0
Event: 154.342 Thread 0x0000021646483700 Thread added: 0x0000021658c3bf00
Event: 154.350 Thread 0x0000021646483700 Thread added: 0x0000021658c3da40
Event: 154.377 Thread 0x0000021646483700 Thread added: 0x0000021658c3d370
Event: 154.387 Thread 0x0000021646483700 Thread added: 0x0000021658c3b830
Event: 154.403 Thread 0x000002164646efc0 Thread added: 0x0000021658c41790


Dynamic libraries:
0x00007ff76ff80000 - 0x00007ff76ff8a000 	C:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffd53be0000 - 0x00007ffd53e47000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffd533c0000 - 0x00007ffd53489000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffd51380000 - 0x00007ffd51770000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffd51780000 - 0x00007ffd518cb000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffd270d0000 - 0x00007ffd270e8000 	C:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffd26150000 - 0x00007ffd2616b000 	C:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffd53570000 - 0x00007ffd53735000 	C:\WINDOWS\System32\USER32.dll
0x00007ffd51350000 - 0x00007ffd51377000 	C:\WINDOWS\System32\win32u.dll
0x00007ffd35780000 - 0x00007ffd35a1a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4768_none_3e0c112ce331287c\COMCTL32.dll
0x00007ffd53740000 - 0x00007ffd5376b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffd51f20000 - 0x00007ffd51fc9000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffd50f20000 - 0x00007ffd51058000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffd511e0000 - 0x00007ffd51283000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffd52910000 - 0x00007ffd5293f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffd3ded0000 - 0x00007ffd3dedc000 	C:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffd04780000 - 0x00007ffd0480d000 	C:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ffc48dc0000 - 0x00007ffc49a4b000 	C:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffd53ae0000 - 0x00007ffd53b94000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffd53a30000 - 0x00007ffd53ad6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffd53820000 - 0x00007ffd53938000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffd537a0000 - 0x00007ffd53814000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffd46dc0000 - 0x00007ffd46df5000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffd50b70000 - 0x00007ffd50bce000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffd3ede0000 - 0x00007ffd3edeb000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffd50b50000 - 0x00007ffd50b64000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffd4fa50000 - 0x00007ffd4fa6b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffd36b40000 - 0x00007ffd36b4a000 	C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffd4e220000 - 0x00007ffd4e461000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffd53030000 - 0x00007ffd533b5000 	C:\WINDOWS\System32\combase.dll
0x00007ffd52950000 - 0x00007ffd52a30000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffd30870000 - 0x00007ffd308b3000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffd518d0000 - 0x00007ffd51969000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffd2f3e0000 - 0x00007ffd2f3ee000 	C:\Program Files\Android\Android Studio\jbr\bin\instrument.dll
0x00007ffd0ebb0000 - 0x00007ffd0ebd0000 	C:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffd0e720000 - 0x00007ffd0e738000 	C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffd51fd0000 - 0x00007ffd5271d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffd50d10000 - 0x00007ffd50e83000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffd4e8b0000 - 0x00007ffd4f110000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffd52f10000 - 0x00007ffd53005000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffd53940000 - 0x00007ffd539aa000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffd50c30000 - 0x00007ffd50c59000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffd2a520000 - 0x00007ffd2a530000 	C:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffd492f0000 - 0x00007ffd4940e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffd4ffc0000 - 0x00007ffd5002b000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffd0e700000 - 0x00007ffd0e716000 	C:\Program Files\Android\Android Studio\jbr\bin\nio.dll
0x00007ffd28f20000 - 0x00007ffd28f30000 	C:\Program Files\Android\Android Studio\jbr\bin\verify.dll
0x00007ffd3e0a0000 - 0x00007ffd3e0c7000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00007ffcfcfe0000 - 0x00007ffcfd058000 	C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffd270c0000 - 0x00007ffd270c9000 	C:\Program Files\Android\Android Studio\jbr\bin\management.dll
0x00007ffd26140000 - 0x00007ffd2614b000 	C:\Program Files\Android\Android Studio\jbr\bin\management_ext.dll
0x00007ffd52ce0000 - 0x00007ffd52ce8000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffd4f4f0000 - 0x00007ffd4f523000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffd52ea0000 - 0x00007ffd52eaa000 	C:\WINDOWS\System32\NSI.dll
0x00007ffd26090000 - 0x00007ffd26099000 	C:\Program Files\Android\Android Studio\jbr\bin\extnet.dll
0x00007ffd503a0000 - 0x00007ffd503bb000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffd4f9b0000 - 0x00007ffd4f9eb000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffd50060000 - 0x00007ffd5008b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffd50c00000 - 0x00007ffd50c26000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffd50200000 - 0x00007ffd5020c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffd36ae0000 - 0x00007ffd36ae8000 	C:\WINDOWS\system32\wshunix.dll
0x00007ffd32d30000 - 0x00007ffd32d63000 	C:\Program Files (x86)\Sangfor\SSL\ClientComponent\1_SangforNspX64.dll
0x00007ffd52cf0000 - 0x00007ffd52e90000 	C:\WINDOWS\System32\ole32.dll
0x00007ffd05260000 - 0x00007ffd05278000 	C:\WINDOWS\system32\napinsp.dll
0x00007ffd4f530000 - 0x00007ffd4f656000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffd05240000 - 0x00007ffd05252000 	C:\WINDOWS\System32\winrnr.dll
0x00007ffd05200000 - 0x00007ffd05230000 	C:\WINDOWS\system32\nlansp_c.dll
0x00007ffd46510000 - 0x00007ffd46530000 	C:\WINDOWS\system32\wshbth.dll
0x00007ffd48c50000 - 0x00007ffd48c5b000 	C:\Windows\System32\rasadhlp.dll
0x00007ffd4a800000 - 0x00007ffd4a886000 	C:\WINDOWS\System32\fwpuclnt.dll
0x00007ffd246e0000 - 0x00007ffd246e7000 	C:\Program Files\Android\Android Studio\jbr\bin\rmi.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4768_none_3e0c112ce331287c;C:\Program Files\Android\Android Studio\jbr\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu;C:\Program Files (x86)\Sangfor\SSL\ClientComponent

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=CN -Duser.language=zh -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.13-bin\5xuhj0ry160q40clulazy9h7d\gradle-8.13\lib\agents\gradle-instrumentation-agent-8.13.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.13
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.13-bin\5xuhj0ry160q40clulazy9h7d\gradle-8.13\lib\gradle-daemon-main-8.13.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 4                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 15                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Users\<USER>\.jdks\corretto-17.0.13
PATH=C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\Program Files\TortoiseSVN\bin;E:\Sdk\platform-tools;C:\Program Files\CorpLink\current\module\mdm\x64\policy\bin;C:\Program Files\dotnet\;D:\Program Files\Git\cmd;E:\apktool;C:\Users\<USER>\.jdks\corretto-17.0.13\bin;E:\apktool\dextools;D:\scrcpy;E:\Sdk\tools;E:\Sdk\build-tools;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Muse Hub\lib;C:\Program Files\PowerShell\7\;D:\Program Files\nodejs\;D:\program files\nodejs\;C:\Users\<USER>\AppData\Local\cloudbase-cli\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Users\dongjs\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\oh-my-posh\bin;C:\Users\<USER>\AppData\Local\Programs\EmEditor;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\Users\dongjs\AppData\Local\Programs\CodeBuddy\bin
USERNAME=dongjs
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 186 Stepping 2, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 42, weak refs: 0

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 874532K (2% of 33272696K total physical memory with 2138764K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 58461K
Loader org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: 39639K
Loader bootstrap                                                                       : 35263K
Loader org.gradle.initialization.MixInLegacyTypesClassLoader                           : 16842K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 8630K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 828K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 797K
Loader org.gradle.groovy.scripts.internal.DefaultScriptCompilationHandler$ScriptClassLoader: 528K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 235K
Loader org.codehaus.groovy.runtime.callsite.CallSiteClassLoader                        : 67336B
Loader sun.reflect.misc.MethodUtil                                                     : 2952B

Classes loaded by more than one classloader:
Class com.google.common.collect.Iterables                                             : loaded 3 times (x 67B)
Class com.google.common.collect.Iterators$MergingIterator                             : loaded 3 times (x 77B)
Class com.google.common.collect.ImmutableList$SubList                                 : loaded 3 times (x 204B)
Class com.google.common.collect.ImmutableSet$SetBuilderImpl                           : loaded 3 times (x 72B)
Class com.google.common.collect.ImmutableList                                         : loaded 3 times (x 202B)
Class com.google.common.collect.RegularImmutableMap$KeySet                            : loaded 3 times (x 146B)
Class com.google.common.base.CharMatcher$InRange                                      : loaded 3 times (x 107B)
Class com.google.common.collect.ImmutableMapEntrySet$RegularEntrySet                  : loaded 3 times (x 147B)
Class com.google.common.base.CharMatcher$NamedFastMatcher                             : loaded 3 times (x 108B)
Class com.google.common.base.CharMatcher$1                                            : loaded 3 times (x 109B)
Class com.google.common.base.Joiner$2                                                 : loaded 3 times (x 75B)
Class com.google.common.base.CharMatcher                                              : loaded 3 times (x 107B)
Class com.google.common.collect.PeekingIterator                                       : loaded 3 times (x 66B)
Class com.google.common.base.Joiner$1                                                 : loaded 3 times (x 76B)
Class com.google.common.base.Preconditions                                            : loaded 3 times (x 67B)
Class com.google.common.collect.Hashing                                               : loaded 3 times (x 67B)
Class com.google.common.base.CharMatcher$Is                                           : loaded 3 times (x 107B)
Class com.google.common.base.CharMatcher$Negated                                      : loaded 3 times (x 109B)
Class com.google.common.collect.RegularImmutableMap$BucketOverflowException           : loaded 3 times (x 78B)
Class com.google.common.base.CharMatcher$IsEither                                     : loaded 3 times (x 107B)
Class com.google.common.collect.ImmutableSet$RegularSetBuilderImpl                    : loaded 3 times (x 73B)
Class org.gradle.internal.IoActions                                                   : loaded 3 times (x 67B)
Class org.gradle.api.GradleException                                                  : loaded 3 times (x 78B)
Class com.google.common.collect.ImmutableEntry                                        : loaded 3 times (x 78B)
Class com.google.common.base.CharMatcher$NegatedFastMatcher                           : loaded 3 times (x 109B)
Class com.google.common.base.Predicate                                                : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableMap                                          : loaded 3 times (x 116B)
Class com.google.common.base.CharMatcher$FastMatcher                                  : loaded 3 times (x 107B)
Class org.gradle.internal.Cast                                                        : loaded 3 times (x 67B)
Class org.gradle.api.Action                                                           : loaded 3 times (x 66B)
Class com.google.common.base.CharMatcher$Or                                           : loaded 3 times (x 108B)
Class com.google.common.base.CharMatcher$BitSetMatcher                                : loaded 3 times (x 108B)
Class com.google.common.collect.RegularImmutableMap$Values                            : loaded 3 times (x 203B)
Class com.google.common.collect.ImmutableSet                                          : loaded 3 times (x 141B)
Class com.google.common.math.IntMath$1                                                : loaded 3 times (x 67B)
Class com.google.common.collect.RegularImmutableList                                  : loaded 3 times (x 208B)
Class com.google.common.collect.ImmutableList$1                                       : loaded 3 times (x 93B)
Class com.google.common.base.Function                                                 : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableMapEntry$NonTerminalImmutableMapEntry        : loaded 3 times (x 81B)
Class com.google.common.collect.ImmutableMapEntry                                     : loaded 3 times (x 81B)
Class com.google.common.math.IntMath                                                  : loaded 3 times (x 67B)
Class com.google.common.base.CharMatcher$IsNot                                        : loaded 3 times (x 107B)
Class com.google.common.collect.RegularImmutableSet                                   : loaded 3 times (x 144B)
Class com.google.common.collect.ImmutableAsList                                       : loaded 3 times (x 205B)
Class com.google.common.collect.Iterators$9                                           : loaded 3 times (x 77B)
Class com.google.common.collect.Lists                                                 : loaded 3 times (x 67B)
Class com.google.common.collect.ImmutableSet$CachingAsList                            : loaded 3 times (x 143B)
Class com.google.common.base.CharMatcher$AnyOf                                        : loaded 3 times (x 108B)
Class com.google.common.collect.ImmutableCollection                                   : loaded 3 times (x 121B)
Class com.google.common.collect.RegularImmutableMap                                   : loaded 3 times (x 117B)
Class com.google.common.collect.ImmutableMap$MapViewOfValuesAsSingletonSets           : loaded 3 times (x 121B)
Class com.google.common.base.CharMatcher$ForPredicate                                 : loaded 3 times (x 108B)
Class com.google.common.collect.ImmutableSet$JdkBackedSetBuilderImpl                  : loaded 3 times (x 72B)
Class com.google.common.collect.Iterators$5                                           : loaded 3 times (x 78B)
Class com.google.common.collect.ImmutableBiMap                                        : loaded 3 times (x 139B)
Class com.google.common.collect.Iterators$4                                           : loaded 3 times (x 78B)
Class com.google.common.collect.ImmutableMap$IteratorBasedImmutableMap                : loaded 3 times (x 121B)
Class com.google.common.collect.SingletonImmutableSet                                 : loaded 3 times (x 142B)
Class com.google.common.collect.Iterators$1                                           : loaded 3 times (x 77B)
Class com.google.common.collect.ImmutableList$ReverseImmutableList                    : loaded 3 times (x 204B)
Class org.gradle.api.UncheckedIOException                                             : loaded 3 times (x 78B)
Class com.google.common.collect.SingletonImmutableList                                : loaded 3 times (x 203B)
Class com.google.common.collect.ImmutableMapEntrySet                                  : loaded 3 times (x 147B)
Class com.google.common.collect.AbstractIndexedListIterator                           : loaded 3 times (x 92B)
Class com.google.common.collect.ObjectArrays                                          : loaded 3 times (x 67B)
Class com.google.common.math.MathPreconditions                                        : loaded 3 times (x 67B)
Class com.google.common.collect.AbstractIterator                                      : loaded 3 times (x 78B)
Class com.google.common.collect.BiMap                                                 : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableMap$1                                        : loaded 3 times (x 77B)
Class com.google.common.collect.IndexedImmutableSet                                   : loaded 3 times (x 146B)
Class com.google.common.collect.UnmodifiableListIterator                              : loaded 3 times (x 91B)
Class com.google.common.base.CharMatcher$And                                          : loaded 3 times (x 108B)
Class com.google.common.collect.Lists$StringAsImmutableList                           : loaded 3 times (x 203B)
Class org.jetbrains.kotlin.tooling.core.KotlinToolingVersion                          : loaded 3 times (x 71B)
Class com.google.common.collect.Iterators                                             : loaded 3 times (x 67B)
Class com.google.common.collect.RegularImmutableAsList                                : loaded 3 times (x 212B)
Class com.google.common.collect.ImmutableList$Builder                                 : loaded 3 times (x 73B)
Class com.google.common.collect.AbstractMapEntry                                      : loaded 3 times (x 77B)
Class com.google.common.collect.UnmodifiableIterator                                  : loaded 3 times (x 76B)
Class com.google.common.collect.Platform                                              : loaded 3 times (x 67B)
Class com.google.common.base.Joiner                                                   : loaded 3 times (x 75B)
Class com.google.common.collect.ImmutableCollection$Builder                           : loaded 3 times (x 72B)
Class com.google.common.collect.Iterators$ArrayItr                                    : loaded 3 times (x 93B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices                    : loaded 2 times (x 76B)
Class [Lorg.jetbrains.kotlin.statistics.ValueAnonymizer;                              : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.idea.gradleTooling.AndroidAwareGradleModelProvider$Companion: loaded 2 times (x 67B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$StreamCloseMode;                   : loaded 2 times (x 65B)
Class com.amazon.ion.impl.lite.IonSymbolLite                                          : loaded 2 times (x 242B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$1     : loaded 2 times (x 76B)
Class com.intellij.util.containers.IntObjectHashMap$ArrayProducer                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.noarg.NoArgModel                  : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.DefaultClassLoaderFactory                       : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.config.LanguageVersionSettings                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Parameters_Decorated: loaded 2 times (x 130B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetWithTests                        : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$None                                         : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.BuildMetricsService                          : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.statistics.metrics.StringAnonymizationPolicy               : loaded 2 times (x 81B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$2     : loaded 2 times (x 76B)
Class com.google.common.collect.ReverseNaturalOrdering                                : loaded 2 times (x 110B)
Class kotlin.sequences.SequencesKt___SequencesKt                                      : loaded 2 times (x 67B)
Class kotlin.collections.AbstractList$Companion                                       : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.ClassLoaderVisitor                              : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetrics               : loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinCompilation;                         : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithJsPresetFunctions      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetComponentWithPublication     : loaded 2 times (x 66B)
Class [Lcom.google.common.collect.ImmutableMapEntry;                                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.report.ReportingSettings$Companion                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.report.ConfigureReporingKt                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.idea.projectModel.KotlinTaskProperties                     : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService: loaded 2 times (x 73B)
Class com.google.common.collect.Ordering                                              : loaded 2 times (x 110B)
Class kotlin.sequences.SequencesKt___SequencesKt$flatMap$2                            : loaded 2 times (x 121B)
Class org.gradle.api.internal.classpath.EffectiveClassPath                            : loaded 2 times (x 86B)
Class com.android.builder.model.v2.models.AndroidDsl                                  : loaded 2 times (x 66B)
Class org.apache.commons.io.output.StringBuilderWriter                                : loaded 2 times (x 96B)
Class [Lorg.jetbrains.kotlin.config.LanguageOrApiVersion;                             : loaded 2 times (x 65B)
Class com.google.common.collect.ImmutableEnumMap                                      : loaded 2 times (x 121B)
Class com.google.common.collect.ListMultimap                                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.TasksProviderKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetFactory        : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.utils.CompatibilityKt                               : loaded 2 times (x 67B)
Class settings_1g60fnx8jpypsisep6rs1ex8k$_run_closure1                                : loaded 2 times (x 135B)
Class com.amazon.ion.impl.lite.IonSystemLite                                          : loaded 2 times (x 271B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode                    : loaded 2 times (x 78B)
Class com.amazon.ion.impl.bin._Private_IonRawWriter                                   : loaded 2 times (x 66B)
Class com.amazon.ion.IonWriter                                                        : loaded 2 times (x 66B)
Class com.intellij.openapi.externalSystem.model.project.dependencies.DependencyNode   : loaded 2 times (x 66B)
Class kotlin.text.StringsKt__AppendableKt                                             : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableRangeSet                                     : loaded 2 times (x 111B)
Class org.gradle.tooling.internal.gradle.DefaultProjectIdentifier                     : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy              : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy$OVERRIDE          : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.incremental.ClasspathChanges                               : loaded 2 times (x 67B)
Class com.google.common.collect.Sets$1                                                : loaded 2 times (x 135B)
Class org.objectweb.asm.AnnotationVisitor                                             : loaded 2 times (x 73B)
Class com.google.common.base.Splitter                                                 : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.internal.MavenPluginConfigurator$MavenPluginConfiguratorVariantFactory: loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_RecyclingQueue$ElementFactory                      : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_IonContainer                                       : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.buildScriptClasspathModel.GradleBuildScriptClasspathSerializationService$ReadContext: loaded 2 times (x 68B)
Class org.gradle.internal.impldep.gnu.trove.TObjectIntHashMap                         : loaded 2 times (x 105B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.allopen.AllOpenModel              : loaded 2 times (x 66B)
Class kotlin.jvm.internal.ArrayIteratorKt                                             : loaded 2 times (x 67B)
Class com.google.common.collect.ComparatorOrdering                                    : loaded 2 times (x 111B)
Class org.gradle.api.internal.DefaultClassPathProvider                                : loaded 2 times (x 72B)
Class com.android.builder.model.v2.models.BasicAndroidProject                         : loaded 2 times (x 66B)
Class com.android.builder.model.AndroidProject                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultConfigurationTimePropertiesAccessor: loaded 2 times (x 70B)
Class com.google.common.collect.Sets$2                                                : loaded 2 times (x 135B)
Class org.jetbrains.kotlin.statistics.metrics.NumericalMetrics                        : loaded 2 times (x 76B)
Class com.amazon.ion.impl._Private_RecyclingStack$$Iterator                           : loaded 2 times (x 95B)
Class com.amazon.ion.impl.SharedSymbolTable                                           : loaded 2 times (x 89B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter                                      : loaded 2 times (x 165B)
Class com.amazon.ion.SymbolToken                                                      : loaded 2 times (x 66B)
Class kotlin.text.StringsKt__StringNumberConversionsJVMKt                             : loaded 2 times (x 67B)
Class com.google.common.collect.NaturalOrdering                                       : loaded 2 times (x 111B)
Class kotlin.text.MatchResult                                                         : loaded 2 times (x 66B)
Class kotlin.sequences.SequenceScope                                                  : loaded 2 times (x 69B)
Class kotlin.sequences.Sequence                                                       : loaded 2 times (x 66B)
Class org.gradle.tooling.ToolingModelContract                                         : loaded 2 times (x 66B)
Class [Lorg.gradle.api.internal.ClassPathProvider;                                    : loaded 2 times (x 65B)
Class org.gradle.api.internal.ClassPathProvider                                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.cli.common.arguments.Argument                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.model.KotlinProject                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.cli.common.CompilerSystemProperties                        : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.config.LanguageVersion$Companion                           : loaded 2 times (x 67B)
Class [Lorg.objectweb.asm.AnnotationVisitor;                                          : loaded 2 times (x 65B)
Class com.google.common.collect.Sets$3                                                : loaded 2 times (x 135B)
Class com.google.common.util.concurrent.AbstractFuture$SafeAtomicHelper               : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$LocalLoadingCache                            : loaded 2 times (x 130B)
Class com.google.common.base.AbstractIterator$State                                   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.PeerNpmDependencyExtension           : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.ExternalSourceDirectorySet                   : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.IonBoolLite                                            : loaded 2 times (x 174B)
Class org.gradle.internal.impldep.gnu.trove.TObjectHash$NULL                          : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt___CollectionsKt$asSequence$$inlined$Sequence$1 : loaded 2 times (x 71B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.config.JvmDefaultMode                                      : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapper                          : loaded 2 times (x 83B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ScriptFilterSpec                : loaded 2 times (x 71B)
Class com.google.common.collect.Sets$4                                                : loaded 2 times (x 135B)
Class org.objectweb.asm.ModuleVisitor                                                 : loaded 2 times (x 76B)
Class com.google.common.cache.LocalCache$Strength$1                                   : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultPeerNpmDependencyExtension    : loaded 2 times (x 138B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget                     : loaded 2 times (x 120B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinSingleTargetExtension                     : loaded 2 times (x 88B)
Class org.gradle.internal.impldep.gnu.trove.PrimeFinder                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.kapt.KaptGradleModel              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinDslScriptAdditionalTask           : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.ClasspathEntryModel                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$addSourcesToKotlinCompileTask$configureAction$2: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.LanguageSettingsBuilder                      : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.SettableFuture                                : loaded 2 times (x 110B)
Class com.google.common.cache.LocalCache$Strength$2                                   : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtension               : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.StringMetrics;                        : loaded 2 times (x 65B)
Class com.amazon.ion.IonTimestamp                                                     : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.PooledBlockAllocatorProvider$PooledBlockAllocator       : loaded 2 times (x 77B)
Class org.jetbrains.plugins.gradle.tooling.serialization.GradleExtensionsSerializationService: loaded 2 times (x 73B)
Class com.android.ide.gradle.model.AdditionalClassifierArtifactsModelParameter        : loaded 2 times (x 66B)
Class org.gradle.api.JavaVersion                                                      : loaded 2 times (x 75B)
Class org.gradle.tooling.model.Dependency                                             : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.AbstractArtifact                               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain$JdkSetter                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1: loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$Strength$3                                   : loaded 2 times (x 77B)
Class com.google.common.collect.MapDifference                                         : loaded 2 times (x 66B)
Class com.google.common.base.Splitter$1                                               : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinCinteropDisambiguation: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.report.BuildReportType$Companion                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginInMultipleProjectsHolder         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.utils.ConfigurationCacheKt                          : loaded 2 times (x 67B)
Class com.amazon.ion.IonDatagram                                                      : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_RecyclingStack$ElementFactory                      : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$1$1   : loaded 2 times (x 74B)
Class com.amazon.ion.impl._Private_ByteTransferSink                                   : loaded 2 times (x 66B)
Class com.intellij.openapi.externalSystem.model.project.dependencies.ResolutionState  : loaded 2 times (x 75B)
Class kotlin.text.StringsKt__RegexExtensionsJVMKt                                     : loaded 2 times (x 67B)
Class com.google.common.collect.AllEqualOrdering                                      : loaded 2 times (x 110B)
Class kotlin.collections.ArraysKt___ArraysJvmKt                                       : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__CollectionsKt                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.targets.native.internal.KotlinNativePlatformDependenciesKt: loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.SubpluginEnvironmentKt$sam$org_gradle_api_Action$0: loaded 2 times (x 71B)
Class [Lorg.jetbrains.kotlin.gradle.dsl.JvmTarget;                                    : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$optInAnnotationsCheck$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinCompilationToRunnableFiles : loaded 2 times (x 177B)
Class org.objectweb.asm.FieldWriter                                                   : loaded 2 times (x 73B)
Class com.google.common.cache.CacheBuilder$NullListener                               : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinMetadataCompatibility : loaded 2 times (x 72B)
Class settings_1g60fnx8jpypsisep6rs1ex8k                                              : loaded 2 times (x 175B)
Class org.jetbrains.plugins.gradle.model.ExternalLibraryDependency                    : loaded 2 times (x 66B)
Class kotlin.text.Regex$Companion                                                     : loaded 2 times (x 67B)
Class org.gradle.internal.classpath.TransformedClassPath                              : loaded 2 times (x 92B)
Class org.gradle.internal.classloader.InstrumentingClassLoader                        : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableListMultimap                                 : loaded 2 times (x 172B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$optInAnnotationsCheck$2: loaded 2 times (x 74B)
Class com.google.common.cache.CacheLoader$FunctionToCacheLoader                       : loaded 2 times (x 71B)
Class com.google.common.base.Suppliers                                                : loaded 2 times (x 67B)
Class [Lcom.google.common.cache.CacheBuilder$OneWeigher;                              : loaded 2 times (x 65B)
Class com.amazon.ion.IonValue                                                         : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableRangeSet$AsSet                               : loaded 2 times (x 296B)
Class kotlin.jvm.internal.FunctionReference                                           : loaded 2 times (x 118B)
Class [Lkotlin.coroutines.intrinsics.CoroutineSingletons;                             : loaded 2 times (x 65B)
Class kotlin.jvm.internal.FunctionBase                                                : loaded 2 times (x 66B)
Class kotlin.enums.EnumEntriesList                                                    : loaded 2 times (x 219B)
Class org.jetbrains.kotlin.config.LanguageFeature                                     : loaded 2 times (x 75B)
Class com.google.common.collect.EmptyImmutableListMultimap                            : loaded 2 times (x 172B)
Class com.google.common.collect.BaseImmutableMultimap                                 : loaded 2 times (x 119B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompileTool$sources$1           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.SourcesUtilsKt                                       : loaded 2 times (x 67B)
Class org.apache.commons.io.output.ByteArrayOutputStream                              : loaded 2 times (x 88B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.TaskConfigAction                : loaded 2 times (x 68B)
Class com.google.common.base.Platform$JdkPatternCompiler                              : loaded 2 times (x 71B)
Class com.google.common.base.MoreObjects                                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinSourceSetFactory               : loaded 2 times (x 76B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$ContainerType                        : loaded 2 times (x 75B)
Class com.amazon.ion.impl._Private_IonTextWriterBuilder$Mutable                       : loaded 2 times (x 98B)
Class kotlin.comparisons.ComparisonsKt___ComparisonsJvmKt                             : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__IteratorsJVMKt                                : loaded 2 times (x 67B)
Class com.android.builder.model.v2.ide.TestInfo$Execution                             : loaded 2 times (x 75B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildAttribute;                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.config.LanguageOrApiVersion                                : loaded 2 times (x 66B)
Class [Lorg.objectweb.asm.Type;                                                       : loaded 2 times (x 65B)
Class org.objectweb.asm.MethodTooLargeException                                       : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain                    : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.utils.IsolatedKotlinClasspathClassCastException     : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinSourceSetContainer                : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.IntelliJSettings                             : loaded 2 times (x 66B)
Class kotlin.Result                                                                   : loaded 2 times (x 68B)
Class org.gradle.internal.service.ServiceLocator                                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJavaToolchainSetter$useAsConvention$1: loaded 2 times (x 121B)
Class com.google.common.base.Ascii                                                    : loaded 2 times (x 67B)
Class com.google.common.collect.SingletonImmutableBiMap                               : loaded 2 times (x 139B)
Class com.google.common.cache.LocalCache$1                                            : loaded 2 times (x 85B)
Class org.gradle.internal.operations.MultipleBuildOperationFailures                   : loaded 2 times (x 91B)
Class org.jetbrains.kotlin.gradle.plugin.internal.IdeaSyncDetector$IdeaSyncDetectorVariantFactory: loaded 2 times (x 66B)
Class com.amazon.ion.impl.IonWriterSystem                                             : loaded 2 times (x 167B)
Class com.amazon.ion.impl.bin.Symbols$2                                               : loaded 2 times (x 103B)
Class com.amazon.ion.impl.bin._Private_IonManagedWriter                               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.android.synthetic.idea.AndroidExtensionsGradleModel        : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.util.GradleVersionUtil                     : loaded 2 times (x 67B)
Class com.android.ide.gradle.model.GradlePropertiesModel                              : loaded 2 times (x 66B)
Class kotlin.sequences.TransformingSequence$iterator$1                                : loaded 2 times (x 75B)
Class kotlin.Pair                                                                     : loaded 2 times (x 68B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$SupportedPropertyInvoker: loaded 2 times (x 72B)
Class org.gradle.tooling.model.UnsupportedMethodException                             : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationWithResources               : loaded 2 times (x 66B)
Class com.google.common.collect.Sets$ImprovedAbstractSet                              : loaded 2 times (x 131B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetPreset                           : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.MethodInvocation                            : loaded 2 times (x 81B)
Class com.google.common.cache.LocalCache$2                                            : loaded 2 times (x 138B)
Class com.google.common.collect.AbstractMultimap                                      : loaded 2 times (x 119B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinJavaRuntimeJarsCompatibility: loaded 2 times (x 72B)
Class com.amazon.ion.impl.lite.IonContainerLite                                       : loaded 2 times (x 244B)
Class com.amazon.ion.IonBufferConfiguration$OversizedSymbolTableHandler               : loaded 2 times (x 66B)
Class com.amazon.ion.system.IonWriterBuilder                                          : loaded 2 times (x 70B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ToolingStreamApiUtils        : loaded 2 times (x 67B)
Class com.amazon.ion.facet.Faceted                                                    : loaded 2 times (x 66B)
Class kotlin.collections.CollectionsKt__MutableCollectionsKt                          : loaded 2 times (x 67B)
Class com.android.builder.model.v2.ide.CodeShrinker                                   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2$1 : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.config.MavenComparableVersion$IntegerItem                  : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain_Decorated          : loaded 2 times (x 122B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinJsCompilation                      : loaded 2 times (x 184B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonOptions                             : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper$1           : loaded 2 times (x 72B)
Class kotlin.sequences.TransformingSequence                                           : loaded 2 times (x 71B)
Class build_8gz8er7t1amukkgp0wql5babj                                                 : loaded 2 times (x 176B)
Class com.amazon.ion.impl.bin.utf8.Poolable                                           : loaded 2 times (x 75B)
Class com.amazon.ion.IonSymbol                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.AnnotationProcessingModelSerializationService$WriteContext: loaded 2 times (x 68B)
Class org.gradle.internal.impldep.gnu.trove.THash                                     : loaded 2 times (x 77B)
Class kotlin.io.TerminateException                                                    : loaded 2 times (x 78B)
Class kotlin.io.FilesKt__FilePathComponentsKt                                         : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableRangeSet$1                                   : loaded 2 times (x 206B)
Class kotlin.reflect.KCallable                                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTimes$Companion                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecker           : loaded 2 times (x 68B)
Class com.google.common.util.concurrent.AbstractFuture$Trusted                        : loaded 2 times (x 66B)
Class com.intellij.util.ThrowableConsumer                                             : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$RangesMatcher                                : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionDelegate       : loaded 2 times (x 165B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport_Decorated               : loaded 2 times (x 120B)
Class org.jetbrains.kotlin.gradle.report.BuildMetricsService$Companion                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.MavenPluginConfigurator$DefaultMavenPluginConfiguratorVariantFactory: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompileTool                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinGradlePluginVersion               : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.IonStructLite                                          : loaded 2 times (x 294B)
Class com.amazon.ion.impl._Private_ReaderWriter                                       : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.AbstractSymbolTable                                     : loaded 2 times (x 103B)
Class com.amazon.ion.Decimal                                                          : loaded 2 times (x 129B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode$1  : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ProjectDependenciesSerializationService$WriteContext: loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ProjectDependenciesSerializationService: loaded 2 times (x 73B)
Class com.intellij.compose.ide.plugin.gradleTooling.rt.ComposeResourcesModel          : loaded 2 times (x 66B)
Class kotlin.collections.AbstractIterator                                             : loaded 2 times (x 79B)
Class kotlin.text.CharsKt                                                             : loaded 2 times (x 67B)
Class com.google.common.collect.Range                                                 : loaded 2 times (x 83B)
Class org.gradle.tooling.model.kotlin.dsl.KotlinDslScriptModel                        : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.CollectionMapper                            : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtension                          : loaded 2 times (x 87B)
Class [Lcom.android.builder.model.v2.ide.LibraryType;                                 : loaded 2 times (x 65B)
Class com.android.builder.model.v2.ide.LibraryType                                    : loaded 2 times (x 75B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildTime;                          : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.SubpluginEnvironment$addSubpluginOptions$compilerOptions$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig         : loaded 2 times (x 68B)
Class com.google.common.util.concurrent.UncheckedExecutionException                   : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$OVERRIDE_VERSION_IF_NOT_SET: loaded 2 times (x 81B)
Class com.amazon.ion.impl.lite.IonFloatLite                                           : loaded 2 times (x 182B)
Class com.amazon.ion.impl._Private_IonTextWriterBuilder                               : loaded 2 times (x 98B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode$2  : loaded 2 times (x 76B)
Class org.gradle.internal.impldep.gnu.trove.Equality                                  : loaded 2 times (x 66B)
Class com.android.ide.gradle.model.ArtifactIdentifierImpl                             : loaded 2 times (x 73B)
Class com.google.common.collect.ExplicitOrdering                                      : loaded 2 times (x 111B)
Class org.jetbrains.kotlin.config.MavenComparableVersion                              : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$scriptSources$1                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttribute$Companion              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt$maybeAddTestDependencyCapability$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinJvmAndroidCompilation              : loaded 2 times (x 178B)
Class com.google.common.base.CharMatcher$JavaLetterOrDigit                            : loaded 2 times (x 107B)
Class org.jetbrains.plugins.gradle.ExternalDependencyId                               : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_RecyclingStack                                     : loaded 2 times (x 75B)
Class com.amazon.ion.impl._Private_IonReaderBuilder                                   : loaded 2 times (x 91B)
Class com.amazon.ion.IonMutableCatalog                                                : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.buildScriptClasspathModel.GradleBuildScriptClasspathSerializationService$WriteContext: loaded 2 times (x 68B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter                      : loaded 2 times (x 76B)
Class org.gradle.internal.InternalTransformer                                         : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.AndroidGradlePluginProjectFlags$BooleanFlag    : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.CompileUsingKotlinDaemonWithNormalization     : loaded 2 times (x 66B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet$1                     : loaded 2 times (x 78B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.HasKotlinDependencies;                     : loaded 2 times (x 65B)
Class org.objectweb.asm.ClassWriter                                                   : loaded 2 times (x 100B)
Class com.google.common.cache.CacheLoader$UnsupportedLoadingOperationException        : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion$getProvider$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultDevNpmDependencyExtension     : loaded 2 times (x 142B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType                           : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Companion          : loaded 2 times (x 67B)
Class org.gradle.internal.impldep.gnu.trove.TObjectCanonicalHashingStrategy           : loaded 2 times (x 76B)
Class kotlin.collections.ArraysKt___ArraysKt$asSequence$$inlined$Sequence$1           : loaded 2 times (x 71B)
Class kotlin.collections.MapsKt__MapsJVMKt                                            : loaded 2 times (x 67B)
Class org.gradle.internal.time.DefaultTimer                                           : loaded 2 times (x 76B)
Class kotlin.collections.EmptyList                                                    : loaded 2 times (x 170B)
Class org.gradle.internal.classloader.ClasspathUtil$1                                 : loaded 2 times (x 72B)
Class com.google.common.base.Objects                                                  : loaded 2 times (x 67B)
Class org.objectweb.asm.FieldVisitor                                                  : loaded 2 times (x 72B)
Class org.objectweb.asm.ClassVisitor                                                  : loaded 2 times (x 83B)
Class [Lorg.objectweb.asm.Symbol;                                                     : loaded 2 times (x 65B)
Class com.android.utils.FileUtils                                                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$ProjectLocalDisambiguation: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$detectKotlinPluginLoadedInMultipleProjects$onRegister$1$1: loaded 2 times (x 71B)
Class com.amazon.ion.impl.bin.PooledBlockAllocatorProvider$PooledBlockAllocator$1     : loaded 2 times (x 75B)
Class com.amazon.ion.impl.lite.IonStringLite                                          : loaded 2 times (x 208B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewKey              : loaded 2 times (x 68B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry$DefaultModule           : loaded 2 times (x 82B)
Class com.android.builder.model.v2.CustomSourceDirectory                              : loaded 2 times (x 66B)
Class com.google.common.primitives.Ints                                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetComponent                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.ResourceUtilsKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt$kotlinPluginVersionFromResources$1: loaded 2 times (x 74B)
Class com.amazon.ion.impl.bin.Block                                                   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.lombok.LombokModel                : loaded 2 times (x 66B)
Class com.google.common.collect.NullsFirstOrdering                                    : loaded 2 times (x 111B)
Class kotlin.ResultKt                                                                 : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$PropertyCachingMethodInvoker: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.UsesKotlinJavaToolchain$DefaultImpls          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributeKind$Companion          : loaded 2 times (x 67B)
Class com.android.builder.model.v2.models.ProjectSyncIssues                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile                         : loaded 2 times (x 397B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationOutput                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinJsTargetDsl                    : loaded 2 times (x 66B)
Class com.google.common.collect.Multiset                                              : loaded 2 times (x 66B)
Class com.google.common.collect.ArrayListMultimapGwtSerializationDependencies         : loaded 2 times (x 168B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$2   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.CacheableTasksKt$cacheOnlyIfEnabledForKotlin$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters$Inject  : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt$kotlinPluginVersionFromResources$1$1: loaded 2 times (x 67B)
Class org.jetbrains.plugins.gradle.model.ExternalTask                                 : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoderPool$1                            : loaded 2 times (x 72B)
Class com.amazon.ion.impl.lite.IonLoaderLite                                          : loaded 2 times (x 78B)
Class com.amazon.ion.impl.lite.IonContext                                             : loaded 2 times (x 66B)
Class com.amazon.ion.impl.IonWriterSystemText                                         : loaded 2 times (x 186B)
Class [Lcom.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode;    : loaded 2 times (x 65B)
Class com.amazon.ion.ValueFactory                                                     : loaded 2 times (x 66B)
Class kotlin.collections.ArraysKt__ArraysKt                                           : loaded 2 times (x 67B)
Class kotlin.Result$Failure                                                           : loaded 2 times (x 68B)
Class org.gradle.internal.time.DefaultCountdownTimer                                  : loaded 2 times (x 84B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformCommonPlugin                   : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.plugin.sources.InternalKotlinSourceSetKt            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptionsDefault_Decorated       : loaded 2 times (x 155B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin$Kapt3SubpluginContext : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$compilerRunner$1        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.HasKotlinDependencies                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultJavaSourceSetsAccessorVariantFactory: loaded 2 times (x 70B)
Class com.amazon.ion.impl._Private_IonReaderBuilder$TwoElementSequenceInputStream     : loaded 2 times (x 88B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolResolver                   : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_SymbolToken                                        : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode    : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.util.ObjectCollector                       : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.model.GradleTaskModel                              : loaded 2 times (x 66B)
Class kotlin.collections.SetsKt__SetsJVMKt                                            : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$AdaptingMethodInvoker: loaded 2 times (x 72B)
Class org.gradle.tooling.internal.gradle.DefaultBuildIdentifier                       : loaded 2 times (x 75B)
Class kotlin.Function                                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.Module                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ClasspathSnapshotProperties_Decorated: loaded 2 times (x 133B)
Class org.jetbrains.kotlin.gradle.report.BuildReportsService$Companion                : loaded 2 times (x 67B)
Class com.google.common.cache.Weigher                                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDirectoryDependencyWithExternalsExtension: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleFinishBuildHandler$Companion     : loaded 2 times (x 67B)
Class com.amazon.ion.impl._Private_IonReaderBuilder$Mutable                           : loaded 2 times (x 91B)
Class com.intellij.openapi.externalSystem.model.project.IExternalSystemSourceType     : loaded 2 times (x 66B)
Class kotlin.io.FileAlreadyExistsException                                            : loaded 2 times (x 78B)
Class kotlin.internal.ProgressionUtilKt                                               : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.UnknownModuleException                        : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.cli.common.arguments.K2JVMCompilerArguments$Companion      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.config.ApiVersion                                          : loaded 2 times (x 82B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsageContext                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetsContainer                       : loaded 2 times (x 66B)
Class com.google.common.base.Stopwatch                                                : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$Strength                                     : loaded 2 times (x 77B)
Class com.google.common.cache.Cache                                                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyWithExternalsExtension  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.GradleCliCommonizerKt                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.PluginWrappersKt                             : loaded 2 times (x 67B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ProjectDependenciesSerializationService$ReadContext: loaded 2 times (x 68B)
Class org.gradle.internal.impldep.gnu.trove.TObjectIdentityHashingStrategy            : loaded 2 times (x 74B)
Class com.intellij.openapi.externalSystem.model.project.dependencies.ProjectDependencies: loaded 2 times (x 66B)
Class ijInit4_4c85cz00e0245cubhlx1uvg1n                                               : loaded 2 times (x 175B)
Class org.jetbrains.kotlin.build.report.metrics.BuildMetricsReporter                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt$defaultSourceSetLanguageSettingsChecker$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.sources.InternalKotlinSourceSet              : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Cancellation                   : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMap$Builder                                  : loaded 2 times (x 78B)
Class com.google.common.base.NullnessCasts                                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonToolArguments$Companion         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.incremental.IncrementalModuleInfo                          : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmTargetDsl                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonToolOptions                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.ConsistencyCheck                     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt$defaultSourceSetLanguageSettingsChecker$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinMultiplatformExtension                    : loaded 2 times (x 466B)
Class com.android.builder.model.Variant                                               : loaded 2 times (x 66B)
Class com.google.common.cache.RemovalListener                                         : loaded 2 times (x 66B)
Class com.amazon.ion.IonBufferConfiguration                                           : loaded 2 times (x 69B)
Class com.amazon.ion.impl.bin.Symbols                                                 : loaded 2 times (x 67B)
Class org.jetbrains.plugins.gradle.tooling.serialization.RepositoriesModelSerializationService: loaded 2 times (x 78B)
Class com.android.ide.gradle.model.artifacts.AdditionalClassifierArtifactsModel       : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.gradle.GradleProjectIdentity                        : loaded 2 times (x 66B)
Class org.gradle.api.internal.DefaultClassPathRegistry                                : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.cli.common.messages.MessageCollector                       : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.config.JvmDefaultMode;                                   : loaded 2 times (x 65B)
Class com.google.common.collect.ImmutableMultimap$Keys                                : loaded 2 times (x 162B)
Class com.android.builder.model.v2.ide.JavaArtifact                                   : loaded 2 times (x 66B)
Class com.android.builder.model.ProjectSyncIssues                                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$unstableFeaturesCheck$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet               : loaded 2 times (x 132B)
Class com.google.common.primitives.IntsMethodsForWeb                                  : loaded 2 times (x 67B)
Class com.google.common.collect.Multimap                                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultPeerNpmDependencyExtension$delegate$1: loaded 2 times (x 165B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency                        : loaded 2 times (x 97B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DefaultNpmDependencyExtension        : loaded 2 times (x 149B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency$Scope                  : loaded 2 times (x 75B)
Class [Lcom.amazon.ion.impl.bin.AbstractIonWriter$WriteValueOptimization;             : loaded 2 times (x 65B)
Class org.jetbrains.plugins.gradle.tooling.serialization.RepositoriesModelSerializationService$ReadContext: loaded 2 times (x 68B)
Class kotlin.ranges.ClosedRange                                                       : loaded 2 times (x 66B)
Class org.gradle.api.internal.ClassPathRegistry                                       : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList              : loaded 2 times (x 195B)
Class com.google.common.base.ExtraObjectsMethodsForWeb                                : loaded 2 times (x 67B)
Class org.apache.commons.io.IOUtils                                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation$DefaultImpls               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerOptionsDefault              : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerToolOptionsDefault          : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$unstableFeaturesCheck$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationTimePropertiesAccessor : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.report.BuildReportMode$Companion                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$VariantImplementationFactory: loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.adapter.InternalBuildIdentifier: loaded 2 times (x 71B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState                        : loaded 2 times (x 81B)
Class com.amazon.ion.impl._Private_IonWriter                                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder                  : loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.projectModel.GradleExternalProjectSerializationService$WriteContext: loaded 2 times (x 69B)
Class kotlin.text.StringsKt__StringsJVMKt                                             : loaded 2 times (x 67B)
Class kotlin.collections.IntIterator                                                  : loaded 2 times (x 78B)
Class com.google.common.collect.NullnessCasts                                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.config.JvmDefaultMode$Companion                            : loaded 2 times (x 67B)
Class com.android.builder.model.v2.dsl.SigningConfig                                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService             : loaded 2 times (x 76B)
Class com.android.builder.model.v2.models.AndroidProject                              : loaded 2 times (x 66B)
Class com.android.builder.model.v2.AndroidModel                                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemVer$Companion                     : loaded 2 times (x 67B)
Class com.google.common.cache.CacheBuilder                                            : loaded 2 times (x 68B)
Class com.android.builder.model.v2.ide.AaptOptions$Namespacing                        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinCinteropCompatibility : loaded 2 times (x 73B)
Class org.jetbrains.kotlin.gradle.dsl.ExplicitApiMode                                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTopLevelExtensionConfig                   : loaded 2 times (x 66B)
Class Program                                                                         : loaded 2 times (x 68B)
Class [Lcom.amazon.ion.SymbolToken;                                                   : loaded 2 times (x 65B)
Class sync_studio_tooling7779_3zv1vv5udlgtw64gsi088kmr7                               : loaded 2 times (x 175B)
Class kotlin.text.StringsKt__IndentKt                                                 : loaded 2 times (x 67B)
Class kotlin.collections.SetsKt                                                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.config.ExplicitApiMode$Companion                           : loaded 2 times (x 67B)
Class org.gradle.internal.classpath.ClassPath                                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.incremental.IncrementalModuleInfoProvider           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptionsDefault                 : loaded 2 times (x 96B)
Class org.gradle.tooling.internal.adapter.TargetTypeProvider                          : loaded 2 times (x 66B)
Class com.google.common.base.JdkPattern                                               : loaded 2 times (x 71B)
Class org.jetbrains.plugins.gradle.model.ExternalProjectDependency                    : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.KotlinDslScriptsModelSerializationService: loaded 2 times (x 77B)
Class kotlin.reflect.KFunction                                                        : loaded 2 times (x 66B)
Class kotlin.collections.ArrayDeque                                                   : loaded 2 times (x 203B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinPluginWrapper                  : loaded 2 times (x 83B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$defaultKotlinJavaToolchain$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.report.BuildReportsService                          : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.daemon.common.MultiModuleICSettings                        : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile                                 : loaded 2 times (x 439B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.arguments.Freezable                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.ObservableSet                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.HierarchyAttributeContainer              : loaded 2 times (x 79B)
Class org.objectweb.asm.SymbolTable$Entry                                             : loaded 2 times (x 70B)
Class com.google.common.util.concurrent.AbstractFuture$AtomicHelper                   : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.UsesKotlinJavaToolchain                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$apply$1              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$OVERRIDE           : loaded 2 times (x 81B)
Class com.amazon.ion.system.IonSystemBuilder$Mutable                                  : loaded 2 times (x 71B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolResolverBuilder            : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ExternalTestsSerializationService$WriteContext: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinGradleModel                       : loaded 2 times (x 66B)
Class kotlin.collections.MapsKt__MapsKt                                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformPluginBase                     : loaded 2 times (x 72B)
Class com.android.builder.model.v2.ide.Variant                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetComponentWithCoordinatesAndPublication: loaded 2 times (x 66B)
Class org.objectweb.asm.Edge                                                          : loaded 2 times (x 68B)
Class com.google.common.base.Suppliers$SupplierOfInstance                             : loaded 2 times (x 75B)
Class kotlin.ranges.IntProgression$Companion                                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$Companion                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultConfigurationTimePropertiesAccessorVariantFactory: loaded 2 times (x 70B)
Class com.amazon.ion.impl._Private_RecyclingStack$Recycler                            : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$LocalSymbolTableView             : loaded 2 times (x 103B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$ThrowingRunnable                     : loaded 2 times (x 66B)
Class com.android.ide.gradle.model.ArtifactIdentifier                                 : loaded 2 times (x 66B)
Class org.gradle.tooling.model.Model                                                  : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.SyncIssue                                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy$SAFE         : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$defaultKotlinJavaToolchain$1    : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain$JavaToolchainSetter       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.SubpluginEnvironment$addSubpluginOptions$configureKotlinTask$1: loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$ComputingValueReference                      : loaded 2 times (x 92B)
Class org.jetbrains.plugins.gradle.model.AnnotationProcessingModel                    : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleExtensions                             : loaded 2 times (x 66B)
Class com.amazon.ion.UnknownSymbolException                                           : loaded 2 times (x 82B)
Class com.android.ide.gradle.model.composites.BuildMap                                : loaded 2 times (x 66B)
Class kotlin.Metadata                                                                 : loaded 2 times (x 66B)
Class kotlin.UninitializedPropertyAccessException                                     : loaded 2 times (x 78B)
Class com.android.builder.model.v2.ide.Library                                        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1$1$1: loaded 2 times (x 75B)
Class com.android.builder.model.v2.models.Versions                                    : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$Segment                                      : loaded 2 times (x 150B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.annotation.AnnotationBasedPluginModel: loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.IntelliJProjectSettings                      : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$MethodInvocationCache: loaded 2 times (x 69B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTimes                            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$ClasspathSnapshotProperties     : loaded 2 times (x 71B)
Class com.android.builder.model.v2.models.ndk.NativeModule                            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2$1     : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt                     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.utils.DescriptionAware                                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet_Decorated     : loaded 2 times (x 183B)
Class [Lorg.objectweb.asm.SymbolTable$Entry;                                          : loaded 2 times (x 65B)
Class com.google.common.cache.CacheLoader                                             : loaded 2 times (x 70B)
Class com.amazon.ion.system.IonBinaryWriterBuilder                                    : loaded 2 times (x 95B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$4                      : loaded 2 times (x 81B)
Class com.google.common.collect.AbstractListMultimap                                  : loaded 2 times (x 168B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy                    : loaded 2 times (x 81B)
Class build_8gz8er7t1amukkgp0wql5babj$_run_closure1                                   : loaded 2 times (x 135B)
Class org.jetbrains.plugins.gradle.model.ExternalDependency                           : loaded 2 times (x 66B)
Class com.intellij.util.containers.IntObjectHashMap                                   : loaded 2 times (x 68B)
Class com.intellij.openapi.externalSystem.model.project.dependencies.AbstractDependencyNode: loaded 2 times (x 83B)
Class com.google.common.collect.SortedIterable                                        : loaded 2 times (x 66B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 71B)
Class org.gradle.api.internal.classpath.ModuleRegistry                                : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.PrivacySandboxSdkInfo                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.logging.GradleKotlinLogger                          : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.model.builder.KotlinModelBuilder$Companion          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1: loaded 2 times (x 75B)
Class com.google.common.util.concurrent.internal.InternalFutureFailureAccess          : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.model.UnresolvedExternalDependency                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultMppTestReportHelperVariantFactory: loaded 2 times (x 70B)
Class com.google.common.base.Splitter$SplittingIterator                               : loaded 2 times (x 80B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages                             : loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$1                    : loaded 2 times (x 76B)
Class com.amazon.ion.impl.LocalSymbolTableAsStruct$Factory                            : loaded 2 times (x 75B)
Class [Lcom.amazon.ion.IonType;                                                       : loaded 2 times (x 65B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService$SourceSetDependencyModelReadContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.dependencyModel.DependencyReadContext: loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.model.VersionCatalogsModel                         : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ObjectGraphAdapter                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.ArgumentUtils                               : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMultimap$Values                              : loaded 2 times (x 122B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanMetrics;                       : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.DefaultKotlinUsageContext                : loaded 2 times (x 103B)
Class org.jetbrains.kotlin.gradle.targets.js.ir.KotlinJsIrTarget                      : loaded 2 times (x 198B)
Class org.jetbrains.kotlin.gradle.plugin.HasCompilerOptions                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinMultiplatformPluginWrapper             : loaded 2 times (x 83B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper             : loaded 2 times (x 74B)
Class com.google.common.cache.LocalCache$LocalManualCache$1                           : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinMetadataDisambiguation: loaded 2 times (x 72B)
Class org.jetbrains.plugins.gradle.model.ProjectImportModelProvider                   : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.ExternalSourceSet                            : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$2                    : loaded 2 times (x 76B)
Class com.amazon.ion.IonNull                                                          : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.warmUp.GradleTaskWarmUpRequest  : loaded 2 times (x 66B)
Class com.android.ide.gradle.model.LegacyAndroidGradlePluginPropertiesModelParameters : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableSortedAsList                                 : loaded 2 times (x 217B)
Class kotlin.text.RegexKt                                                             : loaded 2 times (x 67B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsKt                         : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.config.AnalysisFlag                                        : loaded 2 times (x 68B)
Class com.android.builder.model.v2.models.ModelBuilderParameter                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinSourceSetFactory$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.MppTestReportHelper$MppTestReportHelperVariantFactory: loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$ContainerInfo                        : loaded 2 times (x 70B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$3                    : loaded 2 times (x 76B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$StreamFlushMode                      : loaded 2 times (x 75B)
Class com.amazon.ion.IonBinaryWriter                                                  : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetArtifactIndex.GradleSourceSetArtifactBuildRequest: loaded 2 times (x 66B)
Class kotlin.io.FilesKt__UtilsKt                                                      : loaded 2 times (x 67B)
Class com.google.common.collect.Lists$TransformingRandomAccessList                    : loaded 2 times (x 195B)
Class kotlin.collections.CollectionsKt__CollectionsJVMKt                              : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry                         : loaded 2 times (x 82B)
Class com.google.common.util.concurrent.ListenableFuture                              : loaded 2 times (x 66B)
Class com.google.common.cache.ReferenceEntry                                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinMetadataCompilation                : loaded 2 times (x 66B)
Class com.google.common.base.Converter                                                : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$androidLayoutResources$1        : loaded 2 times (x 74B)
Class com.google.common.base.CharMatcher$Any                                          : loaded 2 times (x 108B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$4                    : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$1: loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.model.ExternalProject                              : loaded 2 times (x 66B)
Class kotlin.sequences.SequencesKt                                                    : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__IteratorsKt                                   : loaded 2 times (x 67B)
Class [Lcom.android.builder.model.v2.ide.CodeShrinker;                                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.CompilerPluginConfig                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.model.builder.KotlinModelBuilder                    : loaded 2 times (x 72B)
Class com.google.common.util.concurrent.AbstractFuture                                : loaded 2 times (x 102B)
Class com.google.common.base.FunctionalEquivalence                                    : loaded 2 times (x 79B)
Class com.android.builder.model.v2.ide.AndroidArtifact                                : loaded 2 times (x 66B)
Class com.google.common.collect.AbstractMapBasedMultimap                              : loaded 2 times (x 135B)
Class org.gradle.api.internal.classpath.ManifestUtil                                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinAndroidTarget                      : loaded 2 times (x 120B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider                           : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin$addKotlinCompilerConfiguration$1: loaded 2 times (x 75B)
Class com.amazon.ion.impl.bin.utf8.Pool                                               : loaded 2 times (x 70B)
Class com.amazon.ion.impl._Private_SymtabExtendsCache                                 : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$2: loaded 2 times (x 79B)
Class kotlin.text.StringsKt                                                           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.config.ExplicitApiMode                                     : loaded 2 times (x 75B)
Class com.google.common.collect.ImmutableMultiset                                     : loaded 2 times (x 159B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$javaVersion$1      : loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilationToRunnableFiles             : loaded 2 times (x 66B)
Class org.objectweb.asm.RecordComponentVisitor                                        : loaded 2 times (x 73B)
Class com.google.common.cache.CacheLoader$SupplierToCacheLoader                       : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTaskKt            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$setupAttributesMatchingStrategy$1$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultIdeaSyncDetectorVariantFactory: loaded 2 times (x 70B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultBasePluginConfigurationVariantFactory: loaded 2 times (x 70B)
Class com.amazon.ion.impl._Private_IonSymbol                                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.IonValueLite                                           : loaded 2 times (x 143B)
Class com.amazon.ion.IonContainer                                                     : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.PooledBlockAllocatorProvider                            : loaded 2 times (x 69B)
Class org.jetbrains.plugins.gradle.tooling.serialization.AnnotationProcessingModelSerializationService$ReadContext: loaded 2 times (x 68B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$InvocationHandlerImpl: loaded 2 times (x 73B)
Class com.android.ide.gradle.model.artifacts.AdditionalClassifierArtifacts            : loaded 2 times (x 66B)
Class kotlin.coroutines.jvm.internal.RestrictedContinuationImpl                       : loaded 2 times (x 83B)
Class org.gradle.util.internal.DefaultGradleVersion$Stage                             : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemverKt                             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$isStdlibAddedByUser$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$sam$java_util_concurrent_Callable$0: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerPluginData                      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.cli.common.CompilerSystemProperties$value$1                : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.tasks.BaseKotlinCompile                             : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.config.LanguageVersion;                                  : loaded 2 times (x 65B)
Class com.google.common.collect.Maps$EntryTransformer                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtensionKt                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters_Decorated: loaded 2 times (x 137B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationTimePropertiesAccessor$ConfigurationTimePropertiesAccessorVariantFactory: loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.IMetricContainerFactory;              : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.idea.gradleTooling.AndroidAwareGradleModelProvider         : loaded 2 times (x 75B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$4: loaded 2 times (x 79B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService$Companion: loaded 2 times (x 67B)
Class [Lkotlin.Pair;                                                                  : loaded 2 times (x 65B)
Class kotlin.text.StringsKt__StringNumberConversionsKt                                : loaded 2 times (x 67B)
Class kotlin.io.FilesKt__FileReadWriteKt                                              : loaded 2 times (x 67B)
Class com.google.common.collect.UsingToStringOrdering                                 : loaded 2 times (x 110B)
Class kotlin.text.MatchNamedGroupCollection                                           : loaded 2 times (x 66B)
Class kotlin.jvm.internal.markers.KMutableList                                        : loaded 2 times (x 66B)
Class org.gradle.internal.time.MonotonicClock                                         : loaded 2 times (x 73B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$isStdlibAddedByUser$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.tasks.GradleCompileTaskProvider_Decorated           : loaded 2 times (x 116B)
Class com.google.common.collect.Lists$ReverseList                                     : loaded 2 times (x 196B)
Class [Lcom.google.common.cache.LocalCache$EntryFactory;                              : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$JavaLetter                                   : loaded 2 times (x 107B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDirectoryDependencyExtension      : loaded 2 times (x 66B)
Class com.amazon.ion.BufferConfiguration$Builder                                      : loaded 2 times (x 74B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$5: loaded 2 times (x 79B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService$SourceSetDependencyModelWriteContext: loaded 2 times (x 68B)
Class [Lkotlin.sequences.Sequence;                                                    : loaded 2 times (x 65B)
Class kotlin.sequences.SequencesKt___SequencesKt$sortedWith$1                         : loaded 2 times (x 71B)
Class com.google.common.collect.ImmutableSortedSet                                    : loaded 2 times (x 296B)
Class org.gradle.internal.classloader.ClassLoaderFactory                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.cli.common.arguments.ArgumentParseErrors                   : loaded 2 times (x 68B)
Class com.android.builder.model.v2.ide.LibraryInfo                                    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$currentJvmJdkToolsJar$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.SubpluginEnvironment                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$addStdlibDependency$1$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.AbstractKotlinSourceSet              : loaded 2 times (x 132B)
Class com.google.common.cache.LocalCache$LocalManualCache                             : loaded 2 times (x 95B)
Class org.gradle.tooling.model.build.GradleEnvironment                                : loaded 2 times (x 66B)
Class com.google.common.base.CommonPattern                                            : loaded 2 times (x 70B)
Class org.jetbrains.kotlin.compilerRunner.GradleCliCommonizerKt$maybeCreateCommonizerClasspathConfiguration$1$1: loaded 2 times (x 75B)
Class com.amazon.ion.impl.bin.IntList                                                 : loaded 2 times (x 73B)
Class com.amazon.ion.impl._Private_RecyclingQueue$ElementIterator                     : loaded 2 times (x 78B)
Class com.amazon.ion.impl.lite._Private_LiteDomTrampoline                             : loaded 2 times (x 67B)
Class com.amazon.ion.IonType                                                          : loaded 2 times (x 75B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$6: loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.tooling.util.IntObjectMap                          : loaded 2 times (x 69B)
Class org.gradle.internal.service.CachingServiceLocator                               : loaded 2 times (x 78B)
Class com.google.common.collect.Streams                                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.internal.BasePluginConfiguration             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetConfiguratorKt$WhenMappings      : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet                       : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.project.model.LanguageSettings                             : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.util.IntObjectMap$ObjectFactory            : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.ValueFactoryLite                                       : loaded 2 times (x 214B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$7: loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.model.RepositoryModels                             : loaded 2 times (x 66B)
Class com.google.common.collect.RegularImmutableSortedSet                             : loaded 2 times (x 296B)
Class kotlin.jvm.internal.CallableReference                                           : loaded 2 times (x 102B)
Class org.jetbrains.kotlin.build.report.metrics.BuildTime                             : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilation$DefaultImpls   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinMetadataTarget                     : loaded 2 times (x 123B)
Class org.objectweb.asm.commons.InstructionAdapter                                    : loaded 2 times (x 183B)
Class com.google.common.collect.Iterators$10                                          : loaded 2 times (x 77B)
Class com.amazon.ion.IonException                                                     : loaded 2 times (x 80B)
Class com.google.common.base.CharMatcher$JavaDigit                                    : loaded 2 times (x 107B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$detectKotlinPluginLoadedInMultipleProjects$onRegister$1: loaded 2 times (x 75B)
Class com.amazon.ion.impl.bin.WriteBuffer                                             : loaded 2 times (x 74B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter                                  : loaded 2 times (x 162B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$8: loaded 2 times (x 79B)
Class [Lorg.gradle.api.JavaVersion;                                                   : loaded 2 times (x 65B)
Class kotlin.ranges.IntRange                                                          : loaded 2 times (x 89B)
Class org.gradle.tooling.model.idea.IdeaDependencyScope                               : loaded 2 times (x 66B)
Class org.gradle.internal.time.TimeSource                                             : loaded 2 times (x 66B)
Class org.gradle.tooling.model.kotlin.dsl.KotlinDslScriptsModel                       : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonCompilerArguments$Companion     : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMultimap                                     : loaded 2 times (x 143B)
Class com.android.builder.model.v2.ide.TestInfo                                       : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetric;             : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinWithJavaTarget                     : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.tasks.CompileUsingKotlinDaemon                      : loaded 2 times (x 66B)
Class com.google.common.base.Equivalence                                              : loaded 2 times (x 78B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$3                      : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.report.BuildReportType                              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleFinishBuildHandler               : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.statistics.metrics.StringOverridePolicy$CONCAT             : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTopLevelExtension                         : loaded 2 times (x 82B)
Class com.amazon.ion.impl.lite.IonBlobLite                                            : loaded 2 times (x 215B)
Class com.amazon.ion.impl.LocalSymbolTable                                            : loaded 2 times (x 117B)
Class org.jetbrains.plugins.gradle.tooling.serialization.SerializationService         : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.gradle.GradleBuildIdentity                          : loaded 2 times (x 66B)
Class kotlin.enums.EnumEntries                                                        : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.JavaCompileOptions                             : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.cli.common.CompilerSystemProperties;                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinTargetWithBinaries                 : loaded 2 times (x 124B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurationsKt                 : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinSourceSet;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetsContainerWithPresets            : loaded 2 times (x 66B)
Class com.google.common.cache.CacheBuilder$OneWeigher                                 : loaded 2 times (x 78B)
Class com.google.common.util.concurrent.ExecutionError                                : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.PropertiesProvider$localProperties$2         : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.internal.JavaSourceSetsAccessor$JavaSourceSetsAccessorVariantFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.IMetricContainerFactory                 : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.ExternalFilter                               : loaded 2 times (x 66B)
Class com.amazon.ion.IonList                                                          : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.adapter.Supplier    : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$MixInMappingAction   : loaded 2 times (x 76B)
Class org.gradle.internal.exceptions.ResolutionProvider                               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.BuildMetricsReporterImpl              : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.SemVer                               : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.internal.ConfigurationTimePropertiesAccessorKt: loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableBiMapFauxverideShim                          : loaded 2 times (x 116B)
Class [Lorg.jetbrains.kotlin.gradle.report.BuildReportType;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompileTool                     : loaded 2 times (x 356B)
Class org.jetbrains.plugins.gradle.model.MavenRepositoryModel                         : loaded 2 times (x 66B)
Class com.amazon.ion.IonSystem                                                        : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolContext            : loaded 2 times (x 68B)
Class org.gradle.tooling.model.idea.IdeaCompilerOutput                                : loaded 2 times (x 66B)
Class org.gradle.tooling.model.ProjectIdentifier                                      : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.arguments.ParseCommandLineArgumentsKt           : loaded 2 times (x 67B)
Class com.android.builder.model.v2.ide.ComponentInfo                                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCompile                                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.JvmTarget$Companion                             : loaded 2 times (x 67B)
Class com.google.common.base.Charsets                                                 : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.Uninterruptibles                              : loaded 2 times (x 67B)
Class com.android.builder.model.v2.ide.ProjectType                                    : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.utils.StringUtilsKt$lowerCamelCaseName$1            : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.tooling.core.HasExtras                                     : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService: loaded 2 times (x 73B)
Class kotlin.io.FileSystemException                                                   : loaded 2 times (x 78B)
Class com.android.builder.model.v2.dsl.BaseConfig                                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinMultiplatformPluginWrapper     : loaded 2 times (x 83B)
Class com.google.common.collect.Maps                                                  : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$JavaUpperCase                                : loaded 2 times (x 107B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.DevNpmDependencyExtension            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.GradleConfigurationUtilsKt$addGradlePluginMetadataAttributes$1: loaded 2 times (x 75B)
Class com.amazon.ion.impl.lite.IonIntLite                                             : loaded 2 times (x 185B)
Class com.amazon.ion.util._Private_FastAppendable                                     : loaded 2 times (x 66B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode;                 : loaded 2 times (x 65B)
Class org.gradle.internal.time.TimeSource$1                                           : loaded 2 times (x 73B)
Class kotlin.jvm.internal.DefaultConstructorMarker                                    : loaded 2 times (x 67B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.GraphItem                                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.internal.DefaultBasePluginConfiguration      : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$disableMultiModuleIC$2          : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.SubpluginEnvironment$Companion               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$Companion: loaded 2 times (x 67B)
Class [Lcom.google.common.collect.ImmutableEntry;                                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTask              : loaded 2 times (x 313B)
Class com.amazon.ion.impl.lite.IonSexpLite                                            : loaded 2 times (x 486B)
Class com.amazon.ion.impl.lite.IonListLite                                            : loaded 2 times (x 486B)
Class com.amazon.ion.IonFloat                                                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$kotlinOptions$1                 : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetric                : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.AbstractKotlinCompileConfig$1$1 : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$LoadingValueReference                        : loaded 2 times (x 92B)
Class org.jetbrains.kotlin.gradle.utils.StringUtilsKt                                 : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.StringOverridePolicy;                 : loaded 2 times (x 65B)
Class com.amazon.ion.impl.LocalSymbolTable$Factory                                    : loaded 2 times (x 73B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode       : loaded 2 times (x 76B)
Class com.intellij.gradle.toolingExtension.impl.model.buildScriptClasspathModel.GradleBuildScriptClasspathSerializationService: loaded 2 times (x 73B)
Class kotlin.jvm.internal.ArrayIterator                                               : loaded 2 times (x 75B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanAnonymizationPolicy;           : loaded 2 times (x 65B)
Class com.google.common.base.Platform                                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTarget                                 : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.FileCollectionDependency                     : loaded 2 times (x 66B)
Class com.amazon.ion.impl.LocalSymbolTableAsStruct                                    : loaded 2 times (x 122B)
Class com.amazon.ion.impl.lite.IonTimestampLite                                       : loaded 2 times (x 188B)
Class org.gradle.internal.impldep.gnu.trove.TObjectHashingStrategy                    : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.utilDummyModel.DummyModel       : loaded 2 times (x 66B)
Class kotlin.collections.EmptyIterator                                                : loaded 2 times (x 85B)
Class kotlin.text.StringsKt__RegexExtensionsKt                                        : loaded 2 times (x 67B)
Class org.gradle.internal.time.Clock                                                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$allNonProjectDependencies$1: loaded 2 times (x 74B)
Class com.android.builder.model.v2.dsl.ProductFlavor                                  : loaded 2 times (x 66B)
Class [Lcom.android.builder.model.v2.ide.TestInfo$Execution;                          : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Parameters  : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.build.report.metrics.BuildAttributeKind;                 : loaded 2 times (x 65B)
Class [Lorg.objectweb.asm.AnnotationWriter;                                           : loaded 2 times (x 65B)
Class com.google.common.primitives.Ints$IntConverter                                  : loaded 2 times (x 86B)
Class com.google.common.collect.Maps$8                                                : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$KotlinUsagesDisambiguation  : loaded 2 times (x 72B)
Class com.amazon.ion.IonString                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleSourceSetDependencyModel               : loaded 2 times (x 66B)
Class kotlin.io.FilePathComponents                                                    : loaded 2 times (x 68B)
Class com.google.common.collect.CompoundOrdering                                      : loaded 2 times (x 111B)
Class kotlin.sequences.SequencesKt__SequencesJVMKt                                    : loaded 2 times (x 67B)
Class kotlin.coroutines.jvm.internal.RestrictedSuspendLambda                          : loaded 2 times (x 87B)
Class org.gradle.tooling.internal.adapter.TypeInspector                               : loaded 2 times (x 69B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$NoOpDecoration       : loaded 2 times (x 75B)
Class org.gradle.tooling.model.Element                                                : loaded 2 times (x 66B)
Class kotlin.collections.AbstractList                                                 : loaded 2 times (x 193B)
Class kotlin.collections.AbstractCollection                                           : loaded 2 times (x 113B)
Class com.google.common.collect.Lists$ReverseList$1                                   : loaded 2 times (x 95B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmOptions                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSetKt             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.HierarchyAttributeContainer$1            : loaded 2 times (x 74B)
Class com.google.common.collect.Sets$SetView                                          : loaded 2 times (x 134B)
Class com.google.common.cache.LocalCache$StrongValueReference                         : loaded 2 times (x 86B)
Class com.google.common.util.concurrent.AbstractFuture$SynchronizedHelper             : loaded 2 times (x 74B)
Class com.google.common.base.CharMatcher$Invisible                                    : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.dsl.ToolchainSupport                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.StringMetrics                           : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.tooling.core.Extras                                        : loaded 2 times (x 66B)
Class com.amazon.ion.BufferConfiguration                                              : loaded 2 times (x 69B)
Class com.amazon.ion.system.SimpleCatalog                                             : loaded 2 times (x 87B)
Class com.intellij.gradle.toolingExtension.impl.model.taskModel.GradleTaskSerialisationService: loaded 2 times (x 72B)
Class com.android.ide.gradle.model.LegacyV1AgpVersionModel                            : loaded 2 times (x 66B)
Class kotlin.text.StringsKt__StringBuilderJVMKt                                       : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableRangeSet$Builder                             : loaded 2 times (x 73B)
Class kotlin.text.Regex                                                               : loaded 2 times (x 68B)
Class kotlin.sequences.SequencesKt__SequenceBuilderKt$sequence$$inlined$Sequence$1    : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tasks.CompilerPluginOptions                         : loaded 2 times (x 68B)
Class [Lorg.jetbrains.kotlin.config.JVMAssertionsMode;                                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.config.JVMAssertionsMode                                   : loaded 2 times (x 75B)
Class org.objectweb.asm.Type                                                          : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.util.IntObjectMap$1                        : loaded 2 times (x 73B)
Class org.gradle.internal.impldep.com.google.common.base.Preconditions                : loaded 2 times (x 67B)
Class org.gradle.tooling.model.internal.ImmutableDomainObjectSet                      : loaded 2 times (x 151B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: loaded 2 times (x 119B)
Class com.android.builder.model.v2.ide.AndroidGradlePluginProjectFlags                : loaded 2 times (x 66B)
Class [Lcom.android.builder.model.v2.ide.AndroidGradlePluginProjectFlags$BooleanFlag; : loaded 2 times (x 65B)
Class [Lorg.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy;            : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.FailedCompilationException                    : loaded 2 times (x 78B)
Class com.android.builder.model.v2.ide.BasicVariant                                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinNativeTarget                       : loaded 2 times (x 125B)
Class org.objectweb.asm.ByteVector                                                    : loaded 2 times (x 74B)
Class [Lcom.google.common.cache.CacheBuilder$NullListener;                            : loaded 2 times (x 65B)
Class com.google.common.collect.ImmutableEnumSet                                      : loaded 2 times (x 142B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Parameters         : loaded 2 times (x 66B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$StreamFlushMode;                   : loaded 2 times (x 65B)
Class com.amazon.ion.UnsupportedIonVersionException                                   : loaded 2 times (x 81B)
Class kotlin.collections.ReversedListReadOnly$listIterator$1                          : loaded 2 times (x 86B)
Class kotlin.comparisons.ComparisonsKt__ComparisonsKt                                 : loaded 2 times (x 67B)
Class kotlin.collections.SetsKt___SetsKt                                              : loaded 2 times (x 67B)
Class kotlin.ranges.RangesKt__RangesKt                                                : loaded 2 times (x 67B)
Class kotlin.sequences.SequencesKt__SequencesKt                                       : loaded 2 times (x 67B)
Class kotlin.coroutines.Continuation                                                  : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$Spec                    : loaded 2 times (x 70B)
Class org.gradle.internal.classpath.DefaultClassPath                                  : loaded 2 times (x 86B)
Class com.android.builder.model.v2.ide.BundleInfo                                     : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.SourceProvider                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerOptions                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.utils.MutableObservableSetImpl                      : loaded 2 times (x 159B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinJsCompilerTypeHolder                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinTasksProvider                           : loaded 2 times (x 68B)
Class org.objectweb.asm.Symbol                                                        : loaded 2 times (x 69B)
Class com.google.common.base.AbstractIterator                                         : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport$wireToolchainToTasks$1  : loaded 2 times (x 75B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState                      : loaded 2 times (x 76B)
Class com.amazon.ion.IonStruct                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleBuildScriptClasspathModel              : loaded 2 times (x 66B)
Class kotlin.sequences.FilteringSequence                                              : loaded 2 times (x 71B)
Class kotlin.text.CharsKt__CharJVMKt                                                  : loaded 2 times (x 67B)
Class kotlin.comparisons.ComparisonsKt                                                : loaded 2 times (x 67B)
Class kotlin.comparisons.ComparisonsKt___ComparisonsKt                                : loaded 2 times (x 67B)
Class kotlin.coroutines.jvm.internal.BaseContinuationImpl                             : loaded 2 times (x 83B)
Class kotlin.Result$Companion                                                         : loaded 2 times (x 67B)
Class org.gradle.util.internal.DefaultGradleVersion                                   : loaded 2 times (x 76B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy                   : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.config.ApiVersion$Companion                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributes                       : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSet$Companion                    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.utils.MutableObservableSet                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.JetBrainsSubpluginArtifact                   : loaded 2 times (x 68B)
Class org.gradle.internal.classloader.ClassLoaderHierarchy                            : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$Ascii                                        : loaded 2 times (x 108B)
Class [Lorg.jetbrains.kotlin.gradle.report.BuildReportMode;                           : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.AbstractKotlinAndroidPluginWrapper           : loaded 2 times (x 83B)
Class org.gradle.tooling.model.GradleModuleVersion                                    : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.ClassLoaderSpec                                 : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJavaToolchainSetter: loaded 2 times (x 71B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributeKind                    : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScopeKt              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationsKt$addSourcesToKotlinCompileTask$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt$configureDefaultVersionsResolutionStrategy$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin$Companion             : loaded 2 times (x 67B)
Class org.objectweb.asm.AnnotationWriter                                              : loaded 2 times (x 74B)
Class com.google.common.cache.CacheStats                                              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTaskKt$buildKotlinToolingMetadataTask$1: loaded 2 times (x 74B)
Class com.amazon.ion.impl.IonWriterSystemTextMarkup                                   : loaded 2 times (x 188B)
Class com.amazon.ion.impl.bin.BlockAllocator                                          : loaded 2 times (x 76B)
Class kotlin.collections.ReversedListReadOnly                                         : loaded 2 times (x 194B)
Class kotlin.jvm.internal.FunctionReferenceImpl                                       : loaded 2 times (x 118B)
Class kotlin.collections.CollectionsKt___CollectionsJvmKt                             : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMultisetGwtSerializationDependencies         : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.config.MavenComparableVersion$ListItem                     : loaded 2 times (x 214B)
Class org.jetbrains.kotlin.gradle.internal.ParcelizeSubplugin                         : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.TaskConfigAction$configureTask$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.dsl.JvmTarget                                       : loaded 2 times (x 75B)
Class [Lcom.android.builder.model.v2.ide.ProjectType;                                 : loaded 2 times (x 65B)
Class org.objectweb.asm.RecordComponentWriter                                         : loaded 2 times (x 74B)
Class com.google.common.util.concurrent.AbstractFuture$Listener                       : loaded 2 times (x 68B)
Class kotlin.collections.CollectionsKt__ReversedViewsKt                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories$Inject        : loaded 2 times (x 92B)
Class com.amazon.ion.impl._Private_RecyclingQueue                                     : loaded 2 times (x 76B)
Class com.amazon.ion.system.IonWriterBuilderBase                                      : loaded 2 times (x 79B)
Class kotlin.TuplesKt                                                                 : loaded 2 times (x 67B)
Class kotlin.jvm.internal.markers.KMutableIterable                                    : loaded 2 times (x 66B)
Class kotlin.sequences.SequencesKt___SequencesJvmKt                                   : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$DefaultViewBuilder   : loaded 2 times (x 76B)
Class kotlin.Unit                                                                     : loaded 2 times (x 67B)
Class [Lcom.android.builder.model.v2.ide.AaptOptions$Namespacing;                     : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile_Decorated                       : loaded 2 times (x 524B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinWasmSubTargetContainerDsl      : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.utils.DescriptionAware;                                  : loaded 2 times (x 65B)
Class org.objectweb.asm.Attribute                                                     : loaded 2 times (x 73B)
Class com.google.common.base.Supplier                                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSetContainer                     : loaded 2 times (x 66B)
Class kotlin.ranges.RangesKt                                                          : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewGraphDetails$1   : loaded 2 times (x 73B)
Class org.gradle.internal.classloader.VisitableURLClassLoader                         : loaded 2 times (x 113B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope;             : loaded 2 times (x 65B)
Class com.google.common.util.concurrent.AbstractFuture$Waiter                         : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport$wireToolchainToTasks$1$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.DefaultKotlinBasePlugin                      : loaded 2 times (x 78B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$1                                    : loaded 2 times (x 73B)
Class [Lcom.amazon.ion.SymbolTable;                                                   : loaded 2 times (x 65B)
Class com.amazon.ion.impl.bin.IonBinaryWriterAdapter$Factory                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.BlockedBuffer$BufferedOutputStream                          : loaded 2 times (x 86B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ExternalTestsSerializationService$ReadContext: loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableList$SerializedForm                          : loaded 2 times (x 69B)
Class kotlin.collections.ArraysUtilJVM                                                : loaded 2 times (x 67B)
Class kotlin.sequences.FlatteningSequence$iterator$1                                  : loaded 2 times (x 75B)
Class kotlin.jvm.functions.Function0                                                  : loaded 2 times (x 66B)
Class kotlin.sequences.SequencesKt__SequenceBuilderKt                                 : loaded 2 times (x 67B)
Class kotlin.jvm.internal.CollectionToArray                                           : loaded 2 times (x 67B)
Class org.gradle.tooling.model.idea.IdeaDependency                                    : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.AaptOptions                                    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithWasmPresetFunctions    : loaded 2 times (x 66B)
Class com.google.common.collect.Sets                                                  : loaded 2 times (x 67B)
Class org.objectweb.asm.Handler                                                       : loaded 2 times (x 68B)
Class com.google.common.base.Strings                                                  : loaded 2 times (x 67B)
Class com.google.common.base.Ticker                                                   : loaded 2 times (x 68B)
Class kotlin.io.FilesKt__FileTreeWalkKt                                               : loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.AbstractIonWriter$WriteValueOptimization                : loaded 2 times (x 75B)
Class kotlin.collections.AbstractMutableList                                          : loaded 2 times (x 202B)
Class kotlin.jvm.functions.Function1                                                  : loaded 2 times (x 66B)
Class com.android.builder.model.v2.models.ndk.NativeModelBuilderParameter             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$javaSources$1                   : loaded 2 times (x 121B)
Class org.jetbrains.kotlin.gradle.utils.FileUtilsKt                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTarget$DefaultImpls                    : loaded 2 times (x 67B)
Class [Lcom.google.common.cache.LocalCache$Segment;                                   : loaded 2 times (x 65B)
Class [Lcom.google.common.cache.LocalCache$Strength;                                  : loaded 2 times (x 65B)
Class com.google.common.cache.CacheBuilder$1                                          : loaded 2 times (x 81B)
Class kotlin.sequences.FilteringSequence$iterator$1                                   : loaded 2 times (x 75B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService$Companion: loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.BlockAllocatorProvider                                  : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService: loaded 2 times (x 73B)
Class org.jetbrains.plugins.gradle.model.GradleSourceSetModel                         : loaded 2 times (x 66B)
Class kotlin.KotlinNothingValueException                                              : loaded 2 times (x 78B)
Class kotlin.jvm.functions.Function2                                                  : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList$Builder      : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationFactory$DefaultImpls    : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinCommonCompilerToolOptions                 : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache                                              : loaded 2 times (x 183B)
Class com.google.common.cache.CacheBuilder$2                                          : loaded 2 times (x 75B)
Class com.amazon.ion.impl._Private_IonValue                                           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.metadata.KotlinMetadataTargetConfiguratorKt : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.testing.internal.KotlinTestsRegistry$Companion      : loaded 2 times (x 67B)
Class com.amazon.ion.impl.lite.IonSequenceLite                                        : loaded 2 times (x 428B)
Class com.amazon.ion.impl._Private_Utils$1                                            : loaded 2 times (x 85B)
Class com.amazon.ion.impl.SymbolTableAsStruct                                         : loaded 2 times (x 66B)
Class com.android.ide.gradle.model.dependencies.Coordinates                           : loaded 2 times (x 66B)
Class com.google.common.collect.AbstractRangeSet                                      : loaded 2 times (x 110B)
Class kotlin.collections.MapsKt___MapsJvmKt                                           : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.WeakIdentityHashMap$AbsentValueProvider     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.SourceSetsVisibilityInferenceKt      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.AbstractKotlinCompile$kotlinLogger$2          : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.report.metrics.BuildPerformanceMetrics$Companion     : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.internal.KotlinTestDependencyManagementKt           : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.arguments.K2JVMCompilerArguments                : loaded 2 times (x 73B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.ConsistencyCheck;                  : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.config.LanguageVersion                                     : loaded 2 times (x 86B)
Class com.google.common.cache.CacheBuilder$3                                          : loaded 2 times (x 68B)
Class [Lcom.google.common.collect.AbstractMapEntry;                                   : loaded 2 times (x 65B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$2                      : loaded 2 times (x 81B)
Class com.google.common.collect.ArrayListMultimap                                     : loaded 2 times (x 168B)
Class com.google.common.base.AbstractIterator$1                                       : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$Digit                                        : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.report.ReportingSettings                            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.internal.BasePluginConfiguration$BasePluginConfigurationVariantFactory: loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinBasePluginWrapper                      : loaded 2 times (x 83B)
Class com.amazon.ion.impl.bin.AbstractIonWriter                                       : loaded 2 times (x 157B)
Class org.jetbrains.plugins.gradle.tooling.util.ObjectCollector$Processor             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.samWithReceiver.SamWithReceiverModel: loaded 2 times (x 66B)
Class kotlin.jvm.functions.Function4                                                  : loaded 2 times (x 66B)
Class kotlin.collections.EmptySet                                                     : loaded 2 times (x 118B)
Class kotlin.annotation.Target                                                        : loaded 2 times (x 66B)
Class kotlin.collections.CollectionsKt___CollectionsKt                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$Companion                 : loaded 2 times (x 67B)
Class org.gradle.internal.exceptions.NonGradleCauseExceptionsHolder                   : loaded 2 times (x 66B)
Class org.gradle.tooling.model.idea.IdeaModuleDependency                              : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ViewBuilder                                 : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.AndroidLibraryData                             : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.ApiVersion                                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompilerOptions                        : loaded 2 times (x 66B)
Class com.google.common.collect.CollectPreconditions                                  : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$JavaIsoControl                               : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.plugin.ProjectLocalConfigurations$ProjectLocalCompatibility: loaded 2 times (x 72B)
Class com.amazon.ion.system.IonReaderBuilder                                          : loaded 2 times (x 89B)
Class org.jetbrains.plugins.gradle.model.AnnotationProcessingConfig                   : loaded 2 times (x 66B)
Class com.amazon.ion.IonReader                                                        : loaded 2 times (x 66B)
Class kotlin.text.StringsKt___StringsJvmKt                                            : loaded 2 times (x 67B)
Class kotlin.reflect.KDeclarationContainer                                            : loaded 2 times (x 66B)
Class kotlin.annotation.Retention                                                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.CompilerPluginOptionsKt                       : loaded 2 times (x 67B)
Class com.android.builder.model.v2.models.Versions$Version                            : loaded 2 times (x 66B)
Class com.google.common.collect.SortedMapDifference                                   : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$JavaLowerCase                                : loaded 2 times (x 107B)
Class com.amazon.ion.IonClob                                                          : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.ModelBuilderService$Parameter              : loaded 2 times (x 66B)
Class kotlin.io.FilesKt                                                               : loaded 2 times (x 67B)
Class kotlin.collections.MapsKt___MapsKt                                              : loaded 2 times (x 67B)
Class kotlin.collections.ArraysKt___ArraysKt                                          : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$DefaultJdkSetter   : loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinOnlyTarget                         : loaded 2 times (x 123B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonCompilerArguments               : loaded 2 times (x 73B)
Class com.google.common.cache.LocalCache$EntryFactory                                 : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.BaseNpmDependencyExtension           : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.dsl.DefaultToolchainSupport                         : loaded 2 times (x 72B)
Class com.amazon.ion.impl.lite.IonLobLite                                             : loaded 2 times (x 180B)
Class com.amazon.ion.impl.lite.IonTextLite                                            : loaded 2 times (x 177B)
Class com.amazon.ion.IonNumber                                                        : loaded 2 times (x 66B)
Class org.gradle.internal.impldep.gnu.trove.TObjectIntProcedure                       : loaded 2 times (x 66B)
Class kotlin.collections.MapsKt__MapWithDefaultKt                                     : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ReflectionMethodInvoker: loaded 2 times (x 72B)
Class org.jetbrains.kotlin.gradle.tasks.TaskOutputsBackup                             : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.internal.StdlibDependencyManagementKt$configureStdlibVersionAlignment$1$1: loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$AbstractReferenceEntry                       : loaded 2 times (x 103B)
Class com.google.common.base.Splitter$1$1                                             : loaded 2 times (x 82B)
Class com.google.common.base.CharMatcher$Whitespace                                   : loaded 2 times (x 108B)
Class org.jetbrains.kotlin.gradle.tasks.TasksProviderKt$sam$org_gradle_api_Action$0   : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.tooling.BuildKotlinToolingMetadataTask$FromKotlinExtension: loaded 2 times (x 313B)
Class org.jetbrains.kotlin.tooling.core.HasMutableExtras                              : loaded 2 times (x 66B)
Class [Lcom.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState;                     : loaded 2 times (x 65B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoder                                  : loaded 2 times (x 76B)
Class com.amazon.ion.IonBool                                                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.assignment.AssignmentModel        : loaded 2 times (x 66B)
Class com.intellij.openapi.externalSystem.model.project.dependencies.ComponentDependencies: loaded 2 times (x 66B)
Class kotlin.text.StringsKt___StringsKt                                               : loaded 2 times (x 67B)
Class com.google.common.collect.NullsLastOrdering                                     : loaded 2 times (x 111B)
Class kotlin.collections.CollectionsKt__MutableCollectionsJVMKt                       : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$DisambiguationRule        : loaded 2 times (x 72B)
Class kotlin.enums.EnumEntriesKt                                                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.arguments.InternalArgument                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy$OR                : loaded 2 times (x 81B)
Class com.android.builder.model.v2.models.VariantDependencies                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3KotlinGradleSubpluginKt               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinJvmCompile                                : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.Kapt3GradleSubplugin                       : loaded 2 times (x 79B)
Class org.objectweb.asm.SymbolTable                                                   : loaded 2 times (x 68B)
Class com.google.common.cache.CacheLoader$InvalidCacheLoadException                   : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.targets.js.npm.NpmDependencyExtensionKt$defaultNpmDependencyDelegate$1: loaded 2 times (x 165B)
Class com.google.common.base.CharMatcher$SingleWidth                                  : loaded 2 times (x 108B)
Class com.intellij.openapi.externalSystem.model.project.dependencies.DependencyScopeNode: loaded 2 times (x 85B)
Class com.amazon.ion.IonLoader                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleProperty                               : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableRangeSet$ComplementRanges                    : loaded 2 times (x 203B)
Class org.gradle.internal.classloader.SystemClassLoaderSpec                           : loaded 2 times (x 67B)
Class com.android.builder.model.v2.ide.TestedTargetVariant                            : loaded 2 times (x 66B)
Class [Lorg.jetbrains.kotlin.statistics.metrics.BooleanOverridePolicy;                : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1$1     : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget$kotlinComponents$2  : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$StrongEntry                                  : loaded 2 times (x 104B)
Class com.google.common.util.concurrent.AbstractFuture$TrustedFuture                  : loaded 2 times (x 110B)
Class com.google.common.cache.LoadingCache                                            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPlatformType$CompatibilityRule         : loaded 2 times (x 72B)
Class com.amazon.ion.impl._Private_Utils                                              : loaded 2 times (x 67B)
Class kotlin.ranges.IntRange$Companion                                                : loaded 2 times (x 67B)
Class kotlin.text.MatcherMatchResult$groups$1                                         : loaded 2 times (x 147B)
Class kotlin.collections.EmptyMap                                                     : loaded 2 times (x 105B)
Class kotlin.reflect.KAnnotatedElement                                                : loaded 2 times (x 66B)
Class kotlin.jvm.internal.markers.KMutableCollection                                  : loaded 2 times (x 66B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt                                       : loaded 2 times (x 67B)
Class org.gradle.internal.exceptions.MultiCauseException                              : loaded 2 times (x 66B)
Class org.gradle.internal.time.CountdownTimer                                         : loaded 2 times (x 66B)
Class kotlin.jvm.internal.Intrinsics                                                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.GradleCompileTaskProvider                     : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.plugin.SubpluginArtifact                            : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinVersion                                   : loaded 2 times (x 75B)
Class org.objectweb.asm.MethodWriter                                                  : loaded 2 times (x 101B)
Class [Lcom.google.common.cache.Weigher;                                              : loaded 2 times (x 65B)
Class [Lcom.google.common.base.AbstractIterator$State;                                : loaded 2 times (x 65B)
Class com.intellij.gradle.toolingExtension.impl.model.projectModel.GradleExternalProjectSerializationService$ReadContext: loaded 2 times (x 69B)
Class com.intellij.gradle.toolingExtension.impl.model.dependencyModel.DependencyWriteContext: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.parcelize.ParcelizeGradleModel    : loaded 2 times (x 66B)
Class kotlin.collections.MapsKt                                                       : loaded 2 times (x 67B)
Class kotlin.coroutines.intrinsics.CoroutineSingletons                                : loaded 2 times (x 75B)
Class kotlin.collections.CollectionsKt__IterablesKt                                   : loaded 2 times (x 67B)
Class org.gradle.tooling.model.ProjectModel                                           : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMultimap$1                                   : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScope$Companion      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.cli.common.CompilerSystemProperties$Companion              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJvmCompile                              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.report.BuildReportMode                              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.statistics.ValueAnonymizer                                 : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleConfiguration                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoderPool                              : loaded 2 times (x 70B)
Class com.amazon.ion.impl._Private_IonSystem                                          : loaded 2 times (x 66B)
Class com.amazon.ion.BufferConfiguration$OversizedValueHandler                        : loaded 2 times (x 66B)
Class kotlin.ranges.OpenEndRange                                                      : loaded 2 times (x 66B)
Class kotlin.sequences.SequenceBuilderIterator                                        : loaded 2 times (x 81B)
Class kotlin.KotlinNullPointerException                                               : loaded 2 times (x 79B)
Class [Lorg.jetbrains.kotlin.config.ExplicitApiMode;                                  : loaded 2 times (x 65B)
Class com.google.common.collect.ImmutableMultimap$2                                   : loaded 2 times (x 77B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$javaExecutable$1   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttribute                        : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilerPluginSupportPlugin            : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinTarget$components$2        : loaded 2 times (x 75B)
Class com.google.common.collect.RegularImmutableBiMap                                 : loaded 2 times (x 144B)
Class com.google.common.cache.LocalCache$ValueReference                               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinNativeCompilation          : loaded 2 times (x 179B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinVariantWithCoordinates             : loaded 2 times (x 104B)
Class [Lorg.jetbrains.kotlin.gradle.targets.js.npm.NpmDependency$Scope;               : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.utils.AndroidPluginIdsKt                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinUsages$setupAttributesMatchingStrategy$1: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinBasePlugin                             : loaded 2 times (x 66B)
Class com.amazon.ion.IonDecimal                                                       : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.tests.ExternalTestsModel                     : loaded 2 times (x 66B)
Class kotlin.ranges.RangesKt___RangesKt                                               : loaded 2 times (x 67B)
Class org.gradle.tooling.model.idea.IdeaLanguageLevel                                 : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$SafeMethodInvoker    : loaded 2 times (x 72B)
Class org.gradle.tooling.internal.adapter.WeakIdentityHashMap$WeakKey                 : loaded 2 times (x 75B)
Class org.gradle.internal.exceptions.DefaultMultiCauseException                       : loaded 2 times (x 91B)
Class com.android.builder.model.v2.ide.ProjectInfo                                    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.internal.KotlinJvmOptionsCompat               : loaded 2 times (x 99B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonToolArguments                   : loaded 2 times (x 68B)
Class com.google.common.util.concurrent.AbstractFuture$Failure                        : loaded 2 times (x 68B)
Class com.google.common.base.PatternCompiler                                          : loaded 2 times (x 66B)
Class com.google.common.base.Equivalence$Equals                                       : loaded 2 times (x 78B)
Class org.gradle.internal.classloader.FilteringClassLoader                            : loaded 2 times (x 101B)
Class kotlin.NotImplementedError                                                      : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.logging.GradleLoggingUtilsKt                        : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinAndroidPluginWrapper                   : loaded 2 times (x 83B)
Class org.jetbrains.plugins.gradle.model.tests.ExternalTestSourceMapping              : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$StreamCloseMode                      : loaded 2 times (x 75B)
Class com.amazon.ion.impl._Private_IonBinaryWriterBuilder$Mutable                     : loaded 2 times (x 111B)
Class com.intellij.gradle.toolingExtension.impl.model.projectModel.GradleExternalProjectSerializationService: loaded 2 times (x 73B)
Class kotlin.text.CharsKt__CharKt                                                     : loaded 2 times (x 67B)
Class kotlin.collections.ArraysKt                                                     : loaded 2 times (x 67B)
Class kotlin.collections.ArrayDeque$Companion                                         : loaded 2 times (x 67B)
Class org.gradle.tooling.model.HierarchicalElement                                    : loaded 2 times (x 66B)
Class com.android.builder.model.v2.dsl.DependenciesInfo                               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunner$Companion              : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithNativeShortcuts        : loaded 2 times (x 66B)
Class kotlin.sequences.FlatteningSequence                                             : loaded 2 times (x 71B)
Class org.objectweb.asm.Label                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.CurrentFrame                                                  : loaded 2 times (x 69B)
Class org.objectweb.asm.ClassTooLargeException                                        : loaded 2 times (x 79B)
Class kotlin.text.MatcherMatchResult                                                  : loaded 2 times (x 76B)
Class kotlin.text.MatchGroupCollection                                                : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PatchPoint                           : loaded 2 times (x 69B)
Class com.amazon.ion.IonBlob                                                          : loaded 2 times (x 66B)
Class com.amazon.ion.IonLob                                                           : loaded 2 times (x 66B)
Class com.amazon.ion.IonSequence                                                      : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ExternalTestsSerializationService: loaded 2 times (x 73B)
Class kotlin.collections.SetsKt__SetsKt                                               : loaded 2 times (x 67B)
Class com.google.common.collect.ByFunctionOrdering                                    : loaded 2 times (x 111B)
Class kotlin.coroutines.EmptyCoroutineContext                                         : loaded 2 times (x 73B)
Class org.gradle.internal.time.Time                                                   : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.config.MavenComparableVersion$Item                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.build.report.metrics.BuildAttributes$Companion             : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilationTask                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.internal.KotlinDependenciesManagementKt             : loaded 2 times (x 67B)
Class kotlin.text.StringsKt__StringsKt                                                : loaded 2 times (x 67B)
Class org.objectweb.asm.Frame                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.MethodVisitor                                                 : loaded 2 times (x 100B)
Class com.google.common.cache.LocalCache$EntryFactory$1                               : loaded 2 times (x 79B)
Class [Lcom.google.common.cache.RemovalListener;                                      : loaded 2 times (x 65B)
Class com.android.ide.gradle.model.dependencies.DeclaredDependencies                  : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$ReadContext: loaded 2 times (x 68B)
Class com.amazon.ion.system.IonTextWriterBuilder                                      : loaded 2 times (x 94B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$1$1$1 : loaded 2 times (x 72B)
Class com.amazon.ion.impl._Private_LocalSymbolTableFactory                            : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.AnnotationProcessingModelSerializationService: loaded 2 times (x 73B)
Class com.google.common.collect.RangeGwtSerializationDependencies                     : loaded 2 times (x 67B)
Class kotlin.collections.ArraysKt__ArraysJVMKt                                        : loaded 2 times (x 67B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.sources.android.AndroidVariantType;        : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.gradle.plugin.sources.android.AndroidVariantType           : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinVariant                            : loaded 2 times (x 97B)
Class com.google.common.cache.LocalCache$EntryFactory$2                               : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.plugin.VariantImplementationFactories               : loaded 2 times (x 72B)
Class com.amazon.ion.impl.lite.IonNullLite                                            : loaded 2 times (x 171B)
Class [Lcom.intellij.openapi.externalSystem.model.project.dependencies.ResolutionState;: loaded 2 times (x 65B)
Class org.gradle.internal.impldep.gnu.trove.TObjectHash                               : loaded 2 times (x 90B)
Class org.jetbrains.plugins.gradle.model.DependencyAccessorsModel                     : loaded 2 times (x 66B)
Class kotlin.text.StringsKt__StringBuilderKt                                          : loaded 2 times (x 67B)
Class com.google.common.collect.RangeSet                                              : loaded 2 times (x 66B)
Class kotlin.jvm.internal.CallableReference$NoReceiver                                : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.GlobalCacheRootsProvider                      : loaded 2 times (x 66B)
Class org.objectweb.asm.ModuleWriter                                                  : loaded 2 times (x 77B)
Class com.google.common.cache.LocalCache$EntryFactory$3                               : loaded 2 times (x 79B)
Class com.google.common.base.Ticker$1                                                 : loaded 2 times (x 68B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$1                      : loaded 2 times (x 81B)
Class com.google.common.collect.Maps$BiMapConverter                                   : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.internal.CompilerArgumentAware                      : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.IonClobLite                                            : loaded 2 times (x 216B)
Class com.amazon.ion.impl._Private_ValueFactory                                       : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode$1                  : loaded 2 times (x 78B)
Class [Lcom.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode; : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinMPPGradleModel                    : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.dependencyDownloadPolicyModel.GradleDependencyDownloadPolicy: loaded 2 times (x 66B)
Class kotlin.NoWhenBranchMatchedException                                             : loaded 2 times (x 78B)
Class org.gradle.tooling.model.BuildIdentifier                                        : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.VectorDrawablesOptions                         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.GradleCompilerRunner                        : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.internal.AndroidExtensionsSubpluginIndicator        : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.gradle.utils.ProviderApiUtilsKt                            : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks            : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$EntryFactory$4                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$Builder                                  : loaded 2 times (x 81B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Inject             : loaded 2 times (x 96B)
Class org.jetbrains.kotlin.gradle.internal.tasks.TaskWithLocalState                   : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.statistics.metrics.BooleanMetrics                          : loaded 2 times (x 76B)
Class settings_1g60fnx8jpypsisep6rs1ex8k$_run_closure1$_closure2                      : loaded 2 times (x 135B)
Class com.amazon.ion.IonInt                                                           : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode$2                  : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.idea.gradleTooling.PrepareKotlinIdeImportTaskModel         : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.sources.KotlinDependencyScopeKt$WhenMappings : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$languageVersionCheck$1: loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$EntryFactory$5                               : loaded 2 times (x 79B)
Class [Lorg.jetbrains.kotlin.gradle.plugin.KotlinPlatformType;                        : loaded 2 times (x 65B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode$3                  : loaded 2 times (x 78B)
Class com.android.ide.gradle.model.GradlePluginModel                                  : loaded 2 times (x 66B)
Class com.google.common.collect.ReverseOrdering                                       : loaded 2 times (x 111B)
Class org.gradle.tooling.model.build.JavaEnvironment                                  : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.config.JVMAssertionsMode$Companion                         : loaded 2 times (x 67B)
Class com.android.builder.model.v2.ide.SourceSetContainer                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$1       : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultLanguageSettingsBuilder       : loaded 2 times (x 86B)
Class org.jetbrains.kotlin.gradle.plugin.sources.FragmentConsistencyChecks$languageVersionCheck$2: loaded 2 times (x 74B)
Class org.jetbrains.kotlin.gradle.dsl.KotlinTargetContainerWithPresetFunctions        : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$6                               : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.utils.GradleConfigurationUtilsKt                    : loaded 2 times (x 67B)
Class org.jetbrains.plugins.gradle.model.GradleExtension                              : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.IonDecimalLite                                         : loaded 2 times (x 184B)
Class com.amazon.ion.IonBufferConfiguration$Builder                                   : loaded 2 times (x 74B)
Class org.gradle.internal.time.Timer                                                  : loaded 2 times (x 66B)
Class kotlin.jvm.internal.markers.KMappedMarker                                       : loaded 2 times (x 66B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMultimap$Builder                             : loaded 2 times (x 79B)
Class com.android.builder.model.v2.ide.LintOptions                                    : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.tasks.DefaultKotlinJavaToolchain$getToolsJarFromJvm$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.tasks.configuration.BaseKotlinCompileConfig$2       : loaded 2 times (x 75B)
Class com.google.common.collect.Maps$KeySet                                           : loaded 2 times (x 133B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.InternalKotlinCompilation                : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$7                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$EmptySetBuilderImpl                      : loaded 2 times (x 72B)
Class com.google.common.base.Equivalence$Identity                                     : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapperKt                        : loaded 2 times (x 67B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$ContainerType;                     : loaded 2 times (x 65B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewGraphDetails     : loaded 2 times (x 68B)
Class org.gradle.tooling.model.idea.IdeaProject                                       : loaded 2 times (x 66B)
Class org.gradle.tooling.model.DomainObjectSet                                        : loaded 2 times (x 66B)
Class org.gradle.internal.service.DefaultServiceLocator                               : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.cli.common.arguments.CommonCompilerArguments$VersionKind   : loaded 2 times (x 75B)
Class com.google.common.collect.ImmutableMultimap$EntryCollection                     : loaded 2 times (x 123B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompile$scriptExtensions$1              : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion$registerIfAbsent$1: loaded 2 times (x 75B)
Class com.android.builder.model.v2.ide.BasicArtifact                                  : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$8                               : loaded 2 times (x 79B)
Class com.google.common.base.PairwiseEquivalence                                      : loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.testing.internal.KotlinTestsRegistry                : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.model.FilePatternSet                               : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoder$Result                           : loaded 2 times (x 70B)
Class com.amazon.ion.impl.bin.utf8.Pool$Allocator                                     : loaded 2 times (x 66B)
Class com.amazon.ion.IonText                                                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.SymbolTokenImpl                                             : loaded 2 times (x 77B)
Class com.amazon.ion.SubstituteSymbolTableException                                   : loaded 2 times (x 80B)
Class kotlin.coroutines.jvm.internal.SuspendFunction                                  : loaded 2 times (x 66B)
Class org.gradle.util.GradleVersion                                                   : loaded 2 times (x 75B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 69B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinCompilerExecutionStrategy               : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.sources.DefaultKotlinSourceSet$special$$inlined$Iterable$1: loaded 2 times (x 75B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation                            : loaded 2 times (x 66B)
Class com.google.common.cache.CacheLoader$1                                           : loaded 2 times (x 71B)
Class com.google.common.collect.ForwardingObject                                      : loaded 2 times (x 68B)
Class org.jetbrains.kotlin.gradle.targets.js.ir.KotlinJsIrCompilation                 : loaded 2 times (x 184B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinTargetConfiguratorKt                   : loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportDescriptor                 : loaded 2 times (x 72B)
Class com.amazon.ion.SymbolTable                                                      : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.GradleExtensionsSerializationService$WriteContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.taskModel.GradleTaskSerialisationService$Companion: loaded 2 times (x 67B)
Class org.jetbrains.plugins.gradle.javaModel.JavaGradleManifestModel                  : loaded 2 times (x 66B)
Class kotlin.jvm.internal.Ref$BooleanRef                                              : loaded 2 times (x 67B)
Class kotlin.jvm.internal.StringCompanionObject                                       : loaded 2 times (x 67B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsJvmKt                      : loaded 2 times (x 67B)
Class kotlin.coroutines.jvm.internal.CoroutineStackFrame                              : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$MethodInvocationCache$MethodInvocationKey: loaded 2 times (x 69B)
Class org.gradle.util.internal.GUtil                                                  : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.KotlinCompilationFactory                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.mpp.AbstractKotlinCompilation                : loaded 2 times (x 170B)
Class com.google.common.util.concurrent.AbstractFuture$SetFuture                      : loaded 2 times (x 71B)
Class com.android.utils.StringHelper                                                  : loaded 2 times (x 67B)
Class com.google.common.cache.AbstractCache$StatsCounter                              : loaded 2 times (x 66B)
Class [Lcom.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState;                   : loaded 2 times (x 65B)
Class com.amazon.ion.impl._Private_IonWriterBase                                      : loaded 2 times (x 158B)
Class com.amazon.ion.system.IonSystemBuilder                                          : loaded 2 times (x 71B)
Class com.amazon.ion.IonCatalog                                                       : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService$SourceSetModelReadContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.utilTurnOffDefaultTasksModel.TurnOffDefaultTasks: loaded 2 times (x 66B)
Class kotlin.io.NoSuchFileException                                                   : loaded 2 times (x 78B)
Class kotlin.collections.CollectionsKt                                                : loaded 2 times (x 67B)
Class com.android.builder.model.v2.ide.ArtifactDependencies                           : loaded 2 times (x 66B)
Class com.android.builder.model.v2.dsl.BuildType                                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.targets.js.dsl.KotlinJsSubTargetContainerDsl        : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.compilerRunner.CompilerSystemPropertiesService$Companion   : loaded 2 times (x 67B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$3: loaded 2 times (x 79B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinGradleBuildServices$Companion$registerIfAbsent$1: loaded 2 times (x 75B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService$SourceSetModelWriteContext: loaded 2 times (x 68B)
Class kotlin.ranges.IntProgressionIterator                                            : loaded 2 times (x 78B)
Class kotlin.coroutines.CoroutineContext                                              : loaded 2 times (x 66B)
Class kotlin.coroutines.jvm.internal.DebugProbesKt                                    : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.WeakIdentityHashMap                         : loaded 2 times (x 72B)
Class org.gradle.tooling.GradleConnectionException                                    : loaded 2 times (x 79B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$1                    : loaded 2 times (x 71B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewDecoration       : loaded 2 times (x 66B)
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 71B)
Class org.jetbrains.kotlin.config.LanguageFeature$State                               : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.gradle.tasks.KotlinJavaToolchain                           : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.ViewBindingOptions                             : loaded 2 times (x 66B)
Class com.google.common.base.Splitter$Strategy                                        : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_RecyclingQueue$Recycler                            : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.ContainerlessContext                                   : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.serialization.GradleExtensionsSerializationService$ReadContext: loaded 2 times (x 68B)
Class com.google.common.collect.LexicographicalOrdering                               : loaded 2 times (x 111B)
Class org.gradle.tooling.internal.adapter.MethodInvoker                               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinCompilation$Companion                  : loaded 2 times (x 67B)
Class com.google.common.collect.Lists$RandomAccessReverseList                         : loaded 2 times (x 196B)
Class org.jetbrains.kotlin.gradle.dsl.ToolchainSupport$Companion                      : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.gradle.plugin.KotlinSourceSet                              : loaded 2 times (x 66B)
Class com.amazon.ion.UnexpectedEofException                                           : loaded 2 times (x 80B)
Class com.amazon.ion.IonSexp                                                          : loaded 2 times (x 66B)
Class com.amazon.ion.ReadOnlyValueException                                           : loaded 2 times (x 80B)
Class com.amazon.ion.impl._Private_LocalSymbolTable                                   : loaded 2 times (x 66B)
Class com.amazon.ion.BufferConfiguration$DataHandler                                  : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_IonBinaryWriterBuilder                             : loaded 2 times (x 111B)
Class org.jetbrains.plugins.gradle.tooling.serialization.RepositoriesModelSerializationService$WriteContext: loaded 2 times (x 68B)
Class com.android.ide.gradle.model.LegacyAndroidGradlePluginProperties                : loaded 2 times (x 66B)
Class kotlin.ranges.IntProgression                                                    : loaded 2 times (x 77B)
Class kotlin.jvm.KotlinReflectionNotSupportedError                                    : loaded 2 times (x 78B)
Class org.gradle.tooling.model.gradle.GradleScript                                    : loaded 2 times (x 66B)

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4768)
OS uptime: 7 days 10:30 hours
Hyper-V role detected

CPU: total 20 (initial active 20) (10 cores per cpu, 2 threads per core) family 6 model 186 stepping 2 microcode 0x4114, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv, serialize, rdtscp, rdpid, fsrm, f16c, cet_ibt, cet_ss
Processor Information for processor 0
  Max Mhz: 2400, Current Mhz: 2400, Mhz Limit: 2400
Processor Information for processor 1
  Max Mhz: 2400, Current Mhz: 1040, Mhz Limit: 2400
Processor Information for processor 2
  Max Mhz: 2400, Current Mhz: 2400, Mhz Limit: 2400
Processor Information for processor 3
  Max Mhz: 2400, Current Mhz: 2400, Mhz Limit: 2400
Processor Information for processor 4
  Max Mhz: 2400, Current Mhz: 2400, Mhz Limit: 2400
Processor Information for processor 5
  Max Mhz: 2400, Current Mhz: 1040, Mhz Limit: 2400
Processor Information for processor 6
  Max Mhz: 2400, Current Mhz: 2400, Mhz Limit: 2400
Processor Information for processor 7
  Max Mhz: 2400, Current Mhz: 2400, Mhz Limit: 2400
Processor Information for processor 8
  Max Mhz: 2400, Current Mhz: 2400, Mhz Limit: 2400
Processor Information for processor 9
  Max Mhz: 2400, Current Mhz: 1040, Mhz Limit: 2400
Processor Information for processor 10
  Max Mhz: 2400, Current Mhz: 2400, Mhz Limit: 2400
Processor Information for processor 11
  Max Mhz: 2400, Current Mhz: 2400, Mhz Limit: 2400
Processor Information for processor 12
  Max Mhz: 2400, Current Mhz: 1200, Mhz Limit: 2400
Processor Information for processor 13
  Max Mhz: 2400, Current Mhz: 1333, Mhz Limit: 2400
Processor Information for processor 14
  Max Mhz: 2400, Current Mhz: 1333, Mhz Limit: 2400
Processor Information for processor 15
  Max Mhz: 2400, Current Mhz: 1333, Mhz Limit: 2400
Processor Information for processor 16
  Max Mhz: 2400, Current Mhz: 1333, Mhz Limit: 2400
Processor Information for processor 17
  Max Mhz: 2400, Current Mhz: 1333, Mhz Limit: 2400
Processor Information for processor 18
  Max Mhz: 2400, Current Mhz: 1200, Mhz Limit: 2400
Processor Information for processor 19
  Max Mhz: 2400, Current Mhz: 1333, Mhz Limit: 2400

Memory: 4k page, system-wide physical 32492M (2101M free)
TotalPageFile size 55092M (AvailPageFile size 65M)
current process WorkingSet (physical memory assigned to process): 827M, peak: 854M
current process commit charge ("private bytes"): 885M, peak: 913M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+-13391695-b895.109) for windows-amd64 JRE (21.0.6+-13391695-b895.109), built on 2025-04-22T21:19:17Z by "builder" with MS VC++ 16.10 / 16.11 (VS2019)

END.
