import com.autoai.car.buildsrc.Versions

apply plugin: 'org.jetbrains.kotlin.android'
apply from: rootProject.file('customize_gradles/app.build.gradle')

android {
    namespace Versions.APPLICATION_ID
    compileSdk Versions.COMPILE_SDK

    defaultConfig {
        applicationId Versions.APPLICATION_ID
        minSdk Versions.MIN_SDK
        targetSdk Versions.TARGET_SDK
        versionCode 1
        versionName "1.0"

        ndk {
            abiFilters 'armeabi', 'armeabi-v7a', 'x86'
        }

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    packagingOptions {
        exclude 'META-INF/com.android.tools/proguard/coroutines.pro'

        //mmkv、unify库冲突问题解决
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/armeabi/libc++_shared.so'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
    }
    kotlinOptions {
        jvmTarget = '11'
    }
}

dependencies {

    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat
    implementation libs.material
    implementation project(':common')
    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espresso.core
}