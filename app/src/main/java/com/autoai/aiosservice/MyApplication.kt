package com.autoai.aiosservice

import android.app.Application
import com.autoai.common.BaseApplication
import com.autoai.common.utils.MMKVUtils
import com.autoai.common.utils.Log

/**
 * @author: 董俊帅
 * @time: 2025/8/26
 * @desc: 应用程序入口类，负责全局初始化
 */
class MyApplication: BaseApplication() {

    override fun onCreate() {
        super.onCreate()

        // 初始化日志系统
        Log.setIsLoggable(true)
        Log.d(TAG, "MyApplication onCreate start")

        try {
            // 初始化 MMKV
            val rootDir = MMKVUtils.initMMKV(this)
            Log.d(TAG, "MMKV initialized successfully, root dir: $rootDir")

            // 其他初始化操作可以在这里添加

            Log.d(TAG, "MyApplication onCreate completed successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error during MyApplication initialization", e)
        }
    }

    companion object {
        private const val TAG = "MyApplication"
    }
}