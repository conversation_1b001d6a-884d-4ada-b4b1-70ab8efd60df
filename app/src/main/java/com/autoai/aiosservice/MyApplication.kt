package com.autoai.aiosservice

import android.content.Context
import com.autoai.aiosservice.utils.AppForegroundUtils
import com.autoai.common.BaseApplication
import com.autoai.common.utils.LogUtils
import com.autoai.common.utils.MMKVUtils

/**
 * @author: 董俊帅
 * @time: 2025/8/26
 * @desc: 
 */class MyApplication: BaseApplication() {

     companion object {
         private const val TAG = "MyApplication"
     }

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        LogUtils.i(TAG, "attachBaseContext")
    }

    override fun onCreate() {
        super.onCreate()
        initDependencies()
    }

    private fun initDependencies() {
        LogUtils.i(TAG, "initDependencies")
        // 基础组件初始化
        MMKVUtils.initMMKV(this)
        // 初始化应用前后台状态监听
        AppForegroundUtils.init(this)

    }
}